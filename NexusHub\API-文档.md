# NexusHub-OH-Back API 文档

## 概述

NexusHub-OH-Back提供了一套完整的RESTful API，为OpenHarmony系统应用商店提供后端支持。所有API均采用JSON格式进行数据交换，并通过JWT和Logto进行身份验证。

### 基本信息

- **基础URL**: `/api/v1`
- **内容类型**: `application/json`
- **版本**: 1.0
- **主机**: `localhost:8080`
- **协议**: `http`
- **Swagger文档**: `/swagger/index.html`
- **服务器状态**: ✅ 运行正常
- **最后更新**: 2025年6月18日

### 项目状态

- ✅ **编译状态**: 所有编译错误已修复
- ✅ **服务器运行**: 后端服务正常运行在 http://localhost:8080
- ✅ **数据库连接**: PostgreSQL 数据库连接正常
- ✅ **Redis缓存**: Redis 缓存服务正常
- ✅ **API路由**: 所有API路由已正确配置
- ✅ **认证系统**: JWT和Logto混合认证服务正常
- ✅ **搜索服务**: Elasticsearch搜索服务集成
- ✅ **消息队列**: RabbitMQ消息队列服务
- ✅ **文件存储**: MinIO对象存储服务
- ✅ **Swagger文档**: API文档自动生成

### 最近修复的问题

1. **控制器方法名修正**:
   - FeedbackController: `GetUserFeedback` → `GetMyFeedback`
   - FeedbackController: `GetFeedback` → `GetFeedbackDetail`
   - HelpCenterController: `ListCategories` → `GetCategories`
   - HelpCenterController: `ListArticles` → `GetArticles`
   - BrowseHistoryController: `GetBrowseHistory` → `GetMyBrowseHistory`
   - BrowseHistoryController: `ClearBrowseHistory` → `ClearMyBrowseHistory`

2. **路由配置修正**:
   - 修正了反馈分类创建路由的控制器调用
   - 统一了所有API路由的方法名称

3. **编译错误修复** (2025年1月):
   - 修复了变量名与包名冲突问题:
     - `featured_collection_controller.go`: 将变量名 `response` 重命名为 `result`
     - `developer_controller.go`: 将变量名 `response` 重命名为 `result`
     - `tag_controller.go`: 修复多处变量名 `response` 与包名冲突
   - 添加了缺失的结构体定义:
     - `TagResponse`: 标签响应结构体
     - `TagAppCountResponse`: 标签应用数量响应结构体
     - `AppResponse`: 应用响应结构体
   - 修复了包引用问题:
     - 修正了 `BadRequest` 函数调用缺少 `response` 包前缀的问题
   - 项目现已成功编译，生成 `nexushub-oh-back` 可执行文件

4. **路由恢复与API文档更新** (2025年6月):
   - 系统性恢复了所有被注释的管理员路由:
     - 开发者管理: 恢复开发者列表、审核、最近申请等3个路由
     - 帮助中心管理: 恢复分类和文章的完整CRUD操作，共9个路由
     - 轮播图管理: 恢复轮播图详情查看路由
     - 意见反馈管理: 恢复反馈回复功能路由
     - OpenHarmony版本管理: 恢复完整的版本管理功能，共5个路由
     - 文件上传: 恢复文件上传凭证获取路由
   - 添加了缺失的文件上传路由组和OpenHarmony版本管理路由组
   - 修复了控制器初始化问题，确保所有路由都有对应的处理函数
   - 全面更新API文档，新增5个功能模块的完整文档
   - 验证了所有恢复路由的功能完整性，确保编译通过

### 响应格式

所有API响应都遵循统一的JSON格式：

```json
{
  "code": 200,       // HTTP状态码
  "message": "操作成功", // 操作结果消息
  "data": {}         // 响应数据（可能是对象、数组或null）
}
```

分页响应格式：

```json
{
  "code": 200,       // HTTP状态码
  "message": "操作成功", // 操作结果消息
  "data": [],        // 数据数组
  "total": 100,      // 总记录数
  "page": 1,         // 当前页码
  "page_size": 20    // 每页数量
}
```

### 错误码

| 状态码 | 描述 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权/认证失败 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 认证

API使用混合认证机制，支持JWT和Logto两种认证方式。除了少数公开接口外，大多数API都需要在请求头中包含有效的认证令牌。

### 认证方式

1. **JWT认证**: 传统的JWT令牌认证
2. **Logto认证**: 基于Logto的OAuth2.0认证
3. **混合认证**: 系统会自动检测并验证两种认证方式

### 获取令牌

通过登录接口获取JWT令牌：

```
POST /api/v1/users/login
```

### 使用令牌

在需要认证的请求中，添加Authorization请求头：

```
Authorization: Bearer {token}
```

## 用户模块

### 注册

**请求**：
```
POST /api/v1/users/register
```

**请求体**：
```json
{
  "username": "johndoe",     // 必填，3-50字符
  "email": "<EMAIL>", // 必填，有效邮箱
  "phone": "***********",    // 可选，11位手机号
  "password": "password123"  // 必填，6-50字符
}
```

**响应**：
```json
{
  "code": 200,
  "message": "注册成功",
  "data": null
}
```

### 登录

**请求**：
```
POST /api/v1/users/login
```

**请求体**：
```json
{
  "username_or_email": "johndoe", // 必填，用户名或邮箱
  "password": "password123"      // 必填，密码
}
```

**响应**：
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2VyX2lkIjoxLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6ImFkbWluIiwiZXhwIjoxNjg4MDQ1MzYwfQ.GYk45OUH7MHk9E4H-4s9NJEXpGBPuuJ3WZJu9j1_GxA",
    "user": {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>",
      "role": "admin"
    }
  }
}
```

### 获取用户资料

**请求**：
```
GET /api/v1/users/profile
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "username": "johndoe",
    "email": "<EMAIL>",
    "phone": "***********",
    "role": "user",
    "status": "active",
    "avatar": "https://example.com/avatar.jpg",
    "created_at": "2023-09-01T12:00:00Z",
    "updated_at": "2023-09-30T12:00:00Z",
    "last_login_at": "2023-09-30T12:00:00Z",
    "login_count": 15,
    "is_developer": false,
    "developer_name": "",
    "company_name": "",
    "website": "",
    "description": "",
    "contact_email": "",
    "contact_phone": "",
    "business_license": "",
    "identity_card": "",
    "developer_avatar": "",
    "developer_address": "",
    "submitted_at": "0001-01-01T00:00:00Z",
    "verified_at": null,
    "verify_reason": "",
    "verify_status": "pending"
  }
}
```

### 更新用户资料

**请求**：
```
PUT /api/v1/users/profile
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "username": "newname",       // 可选，3-50字符
  "email": "<EMAIL>",  // 可选，有效邮箱
  "phone": "***********",      // 可选，11位手机号
  "avatar": "https://example.com/new-avatar.jpg", // 可选，头像URL
  "old_password": "password123", // 可选，当前密码（修改密码时必填）
  "new_password": "newpassword", // 可选，新密码（6-50字符）
  "is_developer": true,        // 可选，是否为开发者
  "developer_info": {          // 可选，开发者信息
    "developer_name": "John Dev", // is_developer为true时必填
    "company_name": "John Inc",
    "website": "https://johndoe.com",
    "description": "Mobile app developer"
  }
}
```

**响应**：
```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": 1,
    "username": "newname",
    "email": "<EMAIL>",
    "phone": "***********",
    "role": "user",
    "status": "active",
    "avatar": "https://example.com/new-avatar.jpg",
    "created_at": "2023-09-01T12:00:00Z",
    "updated_at": "2023-09-30T12:00:00Z",
    "last_login_at": "2023-09-30T12:00:00Z",
    "login_count": 15,
    "is_developer": true,
    "developer_name": "John Dev",
    "company_name": "John Inc",
    "website": "https://johndoe.com",
    "description": "Mobile app developer",
    "contact_email": "<EMAIL>",
    "contact_phone": "***********",
    "business_license": "",
    "identity_card": "",
    "developer_avatar": "",
    "developer_address": "",
    "submitted_at": "0001-01-01T00:00:00Z",
    "verified_at": null,
    "verify_reason": "",
    "verify_status": "pending"
  }
}
```

### 管理员创建用户

**请求**：
```
POST /api/v1/admin/users
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "username": "newuser",       // 必填，3-50字符
  "email": "<EMAIL>", // 必填，有效邮箱
  "phone": "***********",      // 可选，11位手机号
  "password": "password123",   // 必填，6-50字符
  "role": "developer"          // 必填，角色(user/developer/operator/reviewer/admin)
}
```

**响应**：
```json
{
  "code": 200,
  "message": "创建成功",
  "data": null
}
```

### 更新用户角色

**请求**：
```
PUT /api/v1/admin/users/{id}/role
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "role": "operator" // 必填，角色(user/developer/operator/reviewer/admin)
}
```

**响应**：
```json
{
  "code": 200,
  "message": "更新成功",
  "data": null
}
```

## 仪表盘模块

仪表盘模块提供了分析页、监控页和工作台三个主要功能区域，用于展示系统统计数据、监控信息和个人工作台信息。

### 分析页接口

#### 获取分析页摘要数据

**请求**：
```
GET /api/v1/dashboard/analytics/summary
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total_users": 1250,           // 总用户数
    "total_apps": 89,              // 总应用数
    "total_downloads": 15420,      // 总下载量
    "total_reviews": 342,          // 总评论数
    "total_developers": 45,        // 总开发者数
    "new_users_today": 12,         // 今日新增用户
    "new_apps_today": 3,           // 今日新增应用
    "new_downloads_today": 156,    // 今日新增下载
    "pending_apps_count": 8,       // 待审核应用数
    "pending_reviews_count": 5     // 待审核评论数
  }
}
```

#### 获取趋势分析数据

**请求**：
```
GET /api/v1/dashboard/analytics/trend?days=30
```

**请求头**：
```
Authorization: Bearer {token}
```

**查询参数**：
- `days`：可选，统计天数，默认30天

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "user_trend": [
      {"date": "2023-09-01", "value": 45},
      {"date": "2023-09-02", "value": 52}
    ],
    "app_trend": [
      {"date": "2023-09-01", "value": 3},
      {"date": "2023-09-02", "value": 5}
    ],
    "download_trend": [
      {"date": "2023-09-01", "value": 234},
      {"date": "2023-09-02", "value": 289}
    ],
    "developer_trend": [
      {"date": "2023-09-01", "value": 2},
      {"date": "2023-09-02", "value": 3}
    ]
  }
}
```

#### 获取分类统计数据

**请求**：
```
GET /api/v1/dashboard/analytics/categories
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "category_id": 1,
      "category_name": "游戏",
      "app_count": 25,
      "download_count": 5420
    },
    {
      "category_id": 2,
      "category_name": "工具",
      "app_count": 18,
      "download_count": 3210
    }
  ]
}
```

#### 获取热门应用

**请求**：
```
GET /api/v1/dashboard/analytics/popular-apps?limit=10
```

**请求头**：
```
Authorization: Bearer {token}
```

**查询参数**：
- `limit`：可选，返回数量限制，默认10

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "app_id": 1,
      "app_name": "超级游戏",
      "app_icon": "https://example.com/icon1.png",
      "developer_name": "游戏工作室",
      "download_count": 1520,
      "rating": 4.8,
      "category_name": "游戏"
    }
  ]
}
```

### 监控页接口

#### 获取系统监控数据

**请求**：
```
GET /api/v1/dashboard/monitoring/data
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "summary": {
      "server_status": "running",
      "cpu_usage": 45.2,
      "memory_usage": 68.5,
      "disk_usage": 32.1,
      "database_connections": 15,
      "average_response_time": 120.5,
      "requests_per_minute": 450,
      "error_rate": 0.02,
      "uptime_hours": 168.5
    },
    "details": {
      "database": {
        "status": "healthy",
        "connections": 15,
        "max_connections": 100,
        "query_time": 25.3
      },
      "redis": {
        "status": "healthy",
        "memory_usage": 45.2,
        "connected_clients": 8
      },
      "storage": {
        "status": "healthy",
        "total_space": "500GB",
        "used_space": "160GB",
        "available_space": "340GB"
      }
    }
  }
}
```

#### 获取系统日志

**请求**：
```
GET /api/v1/dashboard/monitoring/logs?page=1&page_size=20&level=error
```

**请求头**：
```
Authorization: Bearer {token}
```

**查询参数**：
- `page`：可选，页码，默认1
- `page_size`：可选，每页数量，默认20
- `level`：可选，日志级别过滤（info/warning/error/critical）

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "logs": [
      {
        "id": 1,
        "level": "error",
        "source": "database",
        "message": "连接超时",
        "created_at": "2023-09-30T12:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 156,
      "total_pages": 8
    }
  }
}
```

#### 获取告警事件

**请求**：
```
GET /api/v1/dashboard/monitoring/alerts?page=1&page_size=20&status=active
```

**请求头**：
```
Authorization: Bearer {token}
```

**查询参数**：
- `page`：可选，页码，默认1
- `page_size`：可选，每页数量，默认20
- `status`：可选，状态过滤（active/resolved）

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "alerts": [
      {
        "id": 1,
        "severity": "high",
        "type": "system",
        "description": "CPU使用率过高",
        "status": "active",
        "created_at": "2023-09-30T12:00:00Z",
        "resolved_at": null
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 23,
      "total_pages": 2
    }
  }
}
```

### 工作台接口

#### 获取工作台摘要

**请求**：
```
GET /api/v1/dashboard/workbench/summary
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "my_app_count": 5,              // 我的应用数量
    "total_downloads": 2340,        // 我的应用总下载量
    "average_rating": 4.6,          // 我的应用平均评分
    "new_reviews_count": 8,         // 我的应用新评论数量（最近7天）
    "pending_apps_count": 2,        // 我的待审核应用数量
    "completed_task_count": 12,     // 已完成任务数量
    "total_task_count": 18          // 总任务数量
  }
}
```

#### 获取最近活动

**请求**：
```
GET /api/v1/dashboard/workbench/activities?limit=20
```

**请求头**：
```
Authorization: Bearer {token}
```

**查询参数**：
- `limit`：可选，返回数量限制，默认20

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "user_id": 123,
      "type": "app_create",
      "content": "创建了新应用《超级工具》",
      "created_at": "2023-09-30T12:00:00Z"
    },
    {
      "id": 2,
      "user_id": 123,
      "type": "review",
      "content": "收到了新的应用评论",
      "created_at": "2023-09-30T11:30:00Z"
    }
  ]
}
```

#### 获取任务列表

**请求**：
```
GET /api/v1/dashboard/workbench/tasks?status=pending&page=1&page_size=20
```

**请求头**：
```
Authorization: Bearer {token}
```

**查询参数**：
- `status`：可选，任务状态过滤（pending/in_progress/completed）
- `page`：可选，页码，默认1
- `page_size`：可选，每页数量，默认20

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "tasks": [
      {
        "id": 1,
        "user_id": 123,
        "title": "完成应用审核",
        "description": "审核新提交的应用",
        "status": "pending",
        "priority": "high",
        "due_date": "2023-10-01T18:00:00Z",
        "created_at": "2023-09-30T12:00:00Z",
        "updated_at": "2023-09-30T12:00:00Z",
        "completed_at": null
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 18,
      "total_pages": 1
    }
  }
}
```

#### 创建任务

**请求**：
```
POST /api/v1/dashboard/workbench/tasks
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "title": "完成应用审核",           // 必填，任务标题
  "description": "审核新提交的应用", // 可选，任务描述
  "priority": "high",            // 可选，优先级（low/medium/high），默认medium
  "due_date": "2023-10-01T18:00:00Z" // 可选，截止日期
}
```

**响应**：
```json
{
  "code": 200,
  "message": "创建任务成功",
  "data": {
    "id": 1,
    "user_id": 123,
    "title": "完成应用审核",
    "description": "审核新提交的应用",
    "status": "pending",
    "priority": "high",
    "due_date": "2023-10-01T18:00:00Z",
    "created_at": "2023-09-30T12:00:00Z",
    "updated_at": "2023-09-30T12:00:00Z",
    "completed_at": null
  }
}
```

#### 更新任务

**请求**：
```
PUT /api/v1/dashboard/workbench/tasks/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "title": "完成应用审核（已更新）", // 可选，任务标题
  "description": "审核新提交的应用", // 可选，任务描述
  "status": "completed",         // 可选，任务状态（pending/in_progress/completed）
  "priority": "medium",          // 可选，优先级（low/medium/high）
  "due_date": "2023-10-02T18:00:00Z" // 可选，截止日期
}
```

**响应**：
```json
{
  "code": 200,
  "message": "更新任务成功",
  "data": {
    "id": 1,
    "user_id": 123,
    "title": "完成应用审核（已更新）",
    "description": "审核新提交的应用",
    "status": "completed",
    "priority": "medium",
    "due_date": "2023-10-02T18:00:00Z",
    "created_at": "2023-09-30T12:00:00Z",
    "updated_at": "2023-09-30T14:30:00Z",
    "completed_at": "2023-09-30T14:30:00Z"
  }
}
```

#### 删除任务

**请求**：
```
DELETE /api/v1/dashboard/workbench/tasks/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "删除任务成功",
  "data": null
}
```

## 开发者模块

### 提交开发者认证

**请求**：
```
POST /api/v1/developers/verify
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "developer_name": "John Developer",    // 必填，2-100字符
  "company_name": "John Inc",           // 可选，公司名称
  "contact_email": "<EMAIL>",   // 必填，联系邮箱
  "contact_phone": "***********",       // 必填，联系电话
  "website": "https://johndoe.com",     // 可选，网站URL
  "description": "Mobile app developer focusing on OpenHarmony", // 必填，10-1000字符
  "developer_address": "Beijing, China", // 必填，5-255字符
  "developer_avatar": "https://example.com/dev-avatar.jpg", // 可选，头像URL
  "business_license": "https://example.com/license.jpg",    // 可选，营业执照URL
  "identity_card": "https://example.com/id-card.jpg"        // 必填，身份证URL
}
```

**响应**：
```json
{
  "code": 200,
  "message": "提交成功",
  "data": {
    "id": 1,
    "username": "johndoe",
    "developer_name": "John Developer",
    "company_name": "John Inc",
    "contact_email": "<EMAIL>",
    "contact_phone": "***********",
    "website": "https://johndoe.com",
    "description": "Mobile app developer focusing on OpenHarmony",
    "developer_address": "Beijing, China",
    "developer_avatar": "https://example.com/dev-avatar.jpg",
    "business_license": "https://example.com/license.jpg",
    "identity_card": "https://example.com/id-card.jpg",
    "submitted_at": "2023-10-01T12:00:00Z",
    "verified_at": null,
    "verify_reason": "",
    "verify_status": "pending"
  }
}
```

### 获取认证状态

**请求**：
```
GET /api/v1/developers/verify/status
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "username": "johndoe",
    "developer_name": "John Developer",
    "company_name": "John Inc",
    "contact_email": "<EMAIL>",
    "contact_phone": "***********",
    "website": "https://johndoe.com",
    "description": "Mobile app developer focusing on OpenHarmony",
    "developer_address": "Beijing, China",
    "developer_avatar": "https://example.com/dev-avatar.jpg",
    "business_license": "https://example.com/license.jpg",
    "identity_card": "https://example.com/id-card.jpg",
    "submitted_at": "2023-10-01T12:00:00Z",
    "verified_at": "2023-10-02T10:00:00Z",
    "verify_reason": "",
    "verify_status": "approved"
  }
}
```

### 获取上传凭证

**请求**：
```
GET /api/v1/upload/token
```

**请求头**：
```
Authorization: Bearer {token}
```

**参数**：
- `file_type`: 文件类型(avatar/license/identity/screenshot/package)
- `file_name`: 文件名称

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "file_url": "https://storage.example.com/avatar/user1/avatar.jpg"
  }
}
```

### 管理员获取待审核的开发者列表

**请求**：
```
GET /api/v1/admin/developers/verify
```

**请求头**：
```
Authorization: Bearer {token}
```

**参数**：
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20
- `status`: 认证状态(pending/approved/rejected)，默认pending

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "username": "johndoe",
      "developer_name": "John Developer",
      "company_name": "John Inc",
      "contact_email": "<EMAIL>",
      "contact_phone": "***********",
      "website": "https://johndoe.com",
      "description": "Mobile app developer focusing on OpenHarmony",
      "developer_address": "Beijing, China",
      "developer_avatar": "https://example.com/dev-avatar.jpg",
      "business_license": "https://example.com/license.jpg",
      "identity_card": "https://example.com/id-card.jpg",
      "submitted_at": "2023-10-01T12:00:00Z",
      "verified_at": null,
      "verify_reason": "",
      "verify_status": "pending"
    }
  ],
  "total": 5,
  "page": 1,
  "page_size": 20
}
```

### 管理员审核开发者认证

**请求**：
```
POST /api/v1/admin/developers/{id}/verify
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "verify_status": "approved",  // 必填，审核结果(approved/rejected)
  "verify_reason": "信息完善，符合要求"  // 拒绝时必填，拒绝理由
}
```

**响应**：
```json
{
  "code": 200,
  "message": "审核成功",
  "data": {
    "id": 1,
    "username": "johndoe",
    "developer_name": "John Developer",
    "company_name": "John Inc",
    "contact_email": "<EMAIL>",
    "contact_phone": "***********",
    "website": "https://johndoe.com",
    "description": "Mobile app developer focusing on OpenHarmony",
    "developer_address": "Beijing, China",
    "developer_avatar": "https://example.com/dev-avatar.jpg",
    "business_license": "https://example.com/license.jpg",
    "identity_card": "https://example.com/id-card.jpg",
    "submitted_at": "2023-10-01T12:00:00Z",
    "verified_at": "2023-10-02T10:00:00Z",
    "verify_reason": "信息完善，符合要求",
    "verify_status": "approved"
  }
}
```

## 文件上传模块

### 获取文件上传凭证

**请求**：
```
GET /api/v1/upload/token?file_type=avatar&file_name=avatar.jpg
```

**请求头**：
```
Authorization: Bearer {token}
```

**查询参数**：
- `file_type`: 文件类型，必填
  - `avatar`: 头像文件
  - `license`: 营业执照文件
  - `identity`: 身份证文件
  - `screenshot`: 应用截图文件
  - `package`: 应用安装包文件
- `file_name`: 文件名称，必填

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "upload_url": "https://minio.example.com/nexushub/uploads/avatar/20231001/avatar_123456.jpg?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=...",
    "file_url": "https://minio.example.com/nexushub/uploads/avatar/20231001/avatar_123456.jpg",
    "object_key": "uploads/avatar/20231001/avatar_123456.jpg",
    "expires_in": 3600
  }
}
```

**说明**：
- 此接口需要用户认证
- 返回的 `upload_url` 是预签名的上传URL，有效期1小时
- 使用 PUT 方法将文件上传到 `upload_url`
- 上传成功后，文件可通过 `file_url` 访问
- 支持的文件类型和大小限制：
  - 头像文件：jpg/png，最大2MB
  - 营业执照：jpg/png/pdf，最大5MB
  - 身份证：jpg/png，最大3MB
  - 应用截图：jpg/png，最大5MB
  - 应用安装包：hap，最大100MB

**上传示例**：
```bash
# 1. 获取上传凭证
curl -X GET "https://api.example.com/api/v1/upload/token?file_type=avatar&file_name=avatar.jpg" \
  -H "Authorization: Bearer {token}"

# 2. 使用返回的upload_url上传文件
curl -X PUT "https://minio.example.com/nexushub/uploads/avatar/20231001/avatar_123456.jpg?X-Amz-Algorithm=..." \
  -H "Content-Type: image/jpeg" \
  --data-binary @avatar.jpg
```

## 应用模块

### 公开API接口

#### 获取应用排行

**请求**：
```
GET /api/v1/public/apps/featured
```

**参数**：
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20，最大100

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "Calculator Pro",
      "package": "com.example.calculator",
      "short_desc": "Professional calculator",
      "description": "A professional calculator app for OpenHarmony",
      "icon": "https://example.com/app-icon.jpg",
      "category": "Tools",
      "developer_id": 1,
      "developer_name": "John Developer",
      "current_version": "1.0.0",
      "release_date": "2023-09-15T00:00:00Z",
      "size": 2048000,
      "download_count": 1000,
      "average_rating": 4.5,
      "rating_count": 200,
      "min_open_harmony_os_ver": "3.0.0",
      "tags": "calculator,math,tools",
      "website_url": "https://example.com",
      "privacy_url": "https://example.com/privacy",
      "is_verified": true,
      "is_featured": true,
      "is_editor": false,
      "is_top": false,
      "status": "approved",
      "created_at": "2023-09-01T00:00:00Z",
      "updated_at": "2023-09-15T00:00:00Z"
    }
  ],
  "total": 50,
  "page": 1,
  "page_size": 20
}
```

**说明**：
- 此接口无需认证，任何用户都可以访问
- 只返回状态为"approved"且标记为精选的应用（is_featured=true）
- 按创建时间倒序排列

#### 获取推荐应用

**请求**：
```
GET /api/v1/public/apps/recommended
```

**参数**：
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20，最大100

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "Calculator Pro",
      "package": "com.example.calculator",
      "short_desc": "Professional calculator",
      "description": "A professional calculator app for OpenHarmony",
      "icon": "https://example.com/app-icon.jpg",
      "category": "Tools",
      "developer_id": 1,
      "developer_name": "John Developer",
      "current_version": "1.0.0",
      "release_date": "2023-09-15T00:00:00Z",
      "size": 2048000,
      "download_count": 1000,
      "average_rating": 4.5,
      "rating_count": 200,
      "min_open_harmony_os_ver": "3.0.0",
      "tags": "calculator,math,tools",
      "website_url": "https://example.com",
      "privacy_url": "https://example.com/privacy",
      "is_verified": true,
      "is_featured": true,
      "is_editor": false,
      "is_top": false,
      "status": "approved",
      "created_at": "2023-09-01T00:00:00Z",
      "updated_at": "2023-09-15T00:00:00Z"
    }
  ],
  "total": 50,
  "page": 1,
  "page_size": 20
}
```

**说明**：
- 此接口无需认证，任何用户都可以访问
- 只返回状态为"approved"且标记为推荐的应用
- 按创建时间倒序排列

#### 获取热门应用

**请求**：
```
GET /api/v1/public/apps/popular
```

**参数**：
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20，最大100

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "Calculator Pro",
      "package": "com.example.calculator",
      "short_desc": "Professional calculator",
      "description": "A professional calculator app for OpenHarmony",
      "icon": "https://example.com/app-icon.jpg",
      "category": "Tools",
      "developer_id": 1,
      "developer_name": "John Developer",
      "current_version": "1.0.0",
      "release_date": "2023-09-15T00:00:00Z",
      "size": 2048000,
      "download_count": 1000,
      "average_rating": 4.5,
      "rating_count": 200,
      "min_open_harmony_os_ver": "3.0.0",
      "tags": "calculator,math,tools",
      "website_url": "https://example.com",
      "privacy_url": "https://example.com/privacy",
      "is_verified": true,
      "is_featured": true,
      "is_editor": false,
      "is_top": false,
      "status": "approved",
      "created_at": "2023-09-01T00:00:00Z",
      "updated_at": "2023-09-15T00:00:00Z"
    }
  ],
  "total": 50,
  "page": 1,
  "page_size": 20
}
```

**说明**：
- 此接口无需认证，任何用户都可以访问
- 只返回状态为"approved"的应用
- 按下载量和评分排序

#### 获取最近更新应用

**请求**：
```
GET /api/v1/public/apps/recent
```

**参数**：
- `limit`: 返回数量，默认10个，最大50
- `days`: 最近天数，默认30天，最大365

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "Calculator Pro",
      "package": "com.example.calculator",
      "short_desc": "Professional calculator",
      "description": "A professional calculator app for OpenHarmony",
      "icon": "https://example.com/app-icon.jpg",
      "category": "Tools",
      "developer_id": 1,
      "developer_name": "John Developer",
      "current_version": "1.0.0",
      "release_date": "2023-09-15T00:00:00Z",
      "size": 2048000,
      "download_count": 1000,
      "average_rating": 4.5,
      "rating_count": 200,
      "min_open_harmony_os_ver": "3.0.0",
      "tags": "calculator,math,tools",
      "website_url": "https://example.com",
      "privacy_url": "https://example.com/privacy",
      "is_verified": true,
      "is_featured": true,
      "is_editor": false,
      "is_top": false,
      "status": "approved",
      "created_at": "2023-09-01T00:00:00Z",
      "updated_at": "2023-09-15T00:00:00Z"
    }
  ]
}
```

**说明**：
- 此接口无需认证，任何用户都可以访问
- 只返回状态为"approved"且在指定天数内更新的应用
- 按更新时间倒序排列

#### 获取免费应用

**请求**：
```
GET /api/v1/public/apps/free
```

**参数**：
- `page`: 页码，默认1
- `limit`: 每页数量，默认20，最大100
- `sort`: 排序方式，可选值：popular(热门)、latest(最新)、rating(评分)，默认popular

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "Calculator Pro",
      "package": "com.example.calculator",
      "short_desc": "Professional calculator",
      "description": "A professional calculator app for OpenHarmony",
      "icon": "https://example.com/app-icon.jpg",
      "category": "Tools",
      "developer_id": 1,
      "developer_name": "John Developer",
      "current_version": "1.0.0",
      "release_date": "2023-09-15T00:00:00Z",
      "size": 2048000,
      "download_count": 1000,
      "average_rating": 4.5,
      "rating_count": 200,
      "min_open_harmony_os_ver": "3.0.0",
      "tags": "calculator,math,tools",
      "website_url": "https://example.com",
      "privacy_url": "https://example.com/privacy",
      "is_verified": true,
      "is_featured": true,
      "is_editor": false,
      "is_top": false,
      "status": "approved",
      "created_at": "2023-09-01T00:00:00Z",
      "updated_at": "2023-09-15T00:00:00Z"
    }
  ],
  "total": 50,
  "page": 1,
  "page_size": 20
}
```

**说明**：
- 此接口无需认证，任何用户都可以访问
- 只返回状态为"approved"的免费应用
- 支持多种排序方式：热门(下载量+评分)、最新(创建时间)、评分(平均评分+评分数量)

#### 获取轮播图应用

**请求**：
```
GET /api/v1/public/apps/carousel
```

**参数**：
- `limit`: 返回数量，默认5个，最大20

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "Calculator Pro",
      "package": "com.example.calculator",
      "short_desc": "Professional calculator",
      "icon": "https://example.com/app-icon.jpg",
      "banner_image": "https://example.com/banner.jpg",
      "category": "Tools",
      "developer_name": "John Developer",
      "download_count": 1000,
      "average_rating": 4.5,
      "rating_count": 200,
      "is_verified": true,
      "is_featured": true,
      "is_editor": false,
      "is_top": false,
      "score": 285.5
    }
  ]
}
```

**说明**：
- 此接口无需认证，任何用户都可以访问
- 使用智能算法综合评分，考虑因素包括：
  - 置顶应用：+100分
  - 编辑推荐：+50分
  - 精选应用：+30分
  - 验证开发者：+20分
  - 下载量评分：log10(下载量+1) × 10（最高50分）
  - 评分评分：平均评分 × 10（最高50分）
  - 评分数量：min(评分数量/10, 20)（最高20分）
  - 更新活跃度：根据最近更新时间（最高30分）
  - 有横幅图片：+10分
- 按综合评分倒序排列
- 只返回状态为"approved"的应用

#### 获取最新应用

**请求**：
```
GET /api/v1/public/apps/latest
```

**参数**：
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20，最大100

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "Calculator Pro",
      "package": "com.example.calculator",
      "short_desc": "Professional calculator",
      "description": "A professional calculator app for OpenHarmony",
      "icon": "https://example.com/app-icon.jpg",
      "category": "Tools",
      "developer_id": 1,
      "developer_name": "John Developer",
      "current_version": "1.0.0",
      "release_date": "2023-09-15T00:00:00Z",
      "size": 2048000,
      "download_count": 1000,
      "average_rating": 4.5,
      "rating_count": 200,
      "min_open_harmony_os_ver": "3.0.0",
      "tags": "calculator,math,tools",
      "website_url": "https://example.com",
      "privacy_url": "https://example.com/privacy",
      "is_verified": true,
      "is_featured": false,
      "is_editor": false,
      "is_top": false,
      "status": "approved",
      "created_at": "2023-09-01T00:00:00Z",
      "updated_at": "2023-09-15T00:00:00Z"
    }
  ],
  "total": 100,
  "page": 1,
  "page_size": 20
}
```

**说明**：
- 此接口无需认证，任何用户都可以访问
- 只返回状态为"approved"的应用
- 按下载量和评分排序

#### 获取最新应用

**请求**：
```
GET /api/v1/public/apps/latest
```

**参数**：
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20，最大100

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "Calculator Pro",
      "package": "com.example.calculator",
      "short_desc": "Professional calculator",
      "description": "A professional calculator app for OpenHarmony",
      "icon": "https://example.com/app-icon.jpg",
      "category": "Tools",
      "developer_id": 1,
      "developer_name": "John Developer",
      "current_version": "1.0.0",
      "release_date": "2023-09-15T00:00:00Z",
      "size": 2048000,
      "download_count": 1000,
      "average_rating": 4.5,
      "rating_count": 200,
      "min_open_harmony_os_ver": "3.0.0",
      "tags": "calculator,math,tools",
      "website_url": "https://example.com",
      "privacy_url": "https://example.com/privacy",
      "is_verified": true,
      "is_featured": false,
      "is_editor": false,
      "is_top": false,
      "status": "approved",
      "created_at": "2023-09-01T00:00:00Z",
      "updated_at": "2023-09-15T00:00:00Z"
    }
  ],
  "total": 80,
  "page": 1,
  "page_size": 20
}
```

**说明**：
- 此接口无需认证，任何用户都可以访问
- 只返回状态为"approved"的应用
- 按创建时间倒序排列

#### 获取公开应用列表

**请求**：
```
GET /api/v1/public/apps
```

**参数**：
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20，最大100
- `category`: 应用分类名称，可选
- `keyword`: 搜索关键词，可选
- `sort`: 排序方式，可选值：latest(最新)、popular(热门)、rating(评分)，默认latest

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "Calculator Pro",
      "package": "com.example.calculator",
      "short_desc": "Professional calculator",
      "description": "A professional calculator app for OpenHarmony",
      "icon": "https://example.com/app-icon.jpg",
      "category": "Tools",
      "developer_id": 1,
      "developer_name": "John Developer",
      "current_version": "1.0.0",
      "release_date": "2023-09-15T00:00:00Z",
      "size": 2048000,
      "download_count": 1000,
      "average_rating": 4.5,
      "rating_count": 200,
      "min_open_harmony_os_ver": "3.0.0",
      "tags": "calculator,math,tools",
      "website_url": "https://example.com",
      "privacy_url": "https://example.com/privacy",
      "is_verified": true,
      "is_featured": false,
      "is_editor": false,
      "is_top": false,
      "status": "approved",
      "created_at": "2023-09-01T00:00:00Z",
      "updated_at": "2023-09-15T00:00:00Z"
    }
  ],
  "total": 200,
  "page": 1,
  "page_size": 20
}
```

**说明**：
- 此接口无需认证，任何用户都可以访问
- 只返回状态为"approved"的应用
- 支持分类筛选、关键词搜索和多种排序方式
- 关键词搜索会匹配应用名称、描述和标签

#### 根据分类获取应用

**请求**：
```
GET /api/v1/public/categories/{id}/apps
```

**参数**：
- `id`: 分类ID（路径参数）
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20，最大100
- `sort`: 排序方式，可选值：latest(最新)、popular(热门)、rating(评分)，默认latest

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "Calculator Pro",
      "package": "com.example.calculator",
      "short_desc": "Professional calculator",
      "description": "A professional calculator app for OpenHarmony",
      "icon": "https://example.com/app-icon.jpg",
      "category": "Tools",
      "developer_id": 1,
      "developer_name": "John Developer",
      "current_version": "1.0.0",
      "release_date": "2023-09-15T00:00:00Z",
      "size": 2048000,
      "download_count": 1000,
      "average_rating": 4.5,
      "rating_count": 200,
      "min_open_harmony_os_ver": "3.0.0",
      "tags": "calculator,math,tools",
      "website_url": "https://example.com",
      "privacy_url": "https://example.com/privacy",
      "is_verified": true,
      "is_featured": false,
      "is_editor": false,
      "is_top": false,
      "status": "approved",
      "created_at": "2023-09-01T00:00:00Z",
      "updated_at": "2023-09-15T00:00:00Z"
    }
  ],
  "total": 25,
  "page": 1,
  "page_size": 20
}
```

**说明**：
- 此接口无需认证，任何用户都可以访问
- 只返回状态为"approved"且属于指定分类的应用
- 支持多种排序方式
- 如果分类不存在，返回404错误

#### 获取应用详情

**请求**：
```
GET /api/v1/public/apps/{id}
```

**参数**：
- `id`：应用ID（路径参数）

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "name": "Calculator Pro",
    "package_name": "com.example.calculator",
    "description": "A professional calculator app for OpenHarmony with scientific features.",
    "short_description": "Professional calculator",
    "icon": "https://example.com/app-icon.jpg",
    "category_id": 1,
    "category_name": "Tools",
    "developer_id": 1,
    "developer_name": "John Developer",
    "version": "1.0.0",
    "version_code": 100,
    "min_sdk_version": 21,
    "target_sdk_version": 33,
    "size": 2048000,
    "download_url": "https://example.com/calculator.apk",
    "download_count": 1000,
    "rating": 4.5,
    "review_count": 200,
    "permissions": [
      "android.permission.INTERNET",
      "android.permission.CAMERA"
    ],
    "tags": ["calculator", "math", "tools"],
    "changelog": "A professional calculator app with scientific features.",
    "privacy_policy": "https://calculator-pro.example.com/privacy",
    "support_email": "<EMAIL>",
    "website": "https://calculator-pro.example.com",
    "status": "approved",
    "published_at": "2023-09-15T00:00:00Z",
    "screenshots": [
      {
        "id": 1,
        "application_id": 1,
        "image_url": "https://example.com/screenshot1.jpg",
        "sort_order": 1,
        "created_at": "2023-09-01T00:00:00Z"
      }
    ],
    "published_versions": [
      {
        "id": 1,
        "application_id": 1,
        "version_name": "1.0.0",
        "version_code": 100,
        "change_log": "Initial release",
        "package_url": "https://example.com/calculator-1.0.0.hap",
        "size": 2048000,
        "status": "approved",
        "min_open_harmony_os_ver": "3.0.0",
        "released_at": "2023-09-15T00:00:00Z",
        "download_count": 1000,
        "incremental_update": false,
        "created_at": "2023-09-01T00:00:00Z",
        "updated_at": "2023-09-15T00:00:00Z"
      }
    ]
  }
}
```

**说明**：
- 此接口无需认证，任何用户都可以访问
- 只返回状态为"approved"的应用信息
- 只返回已发布的应用版本
- 包含应用的基本信息、截图和已发布版本列表

#### 记录应用下载

**请求**：
```
POST /api/v1/public/apps/{id}/versions/{version_id}/download
```

**参数**：
- `id`：应用ID（路径参数）
- `version_id`：版本ID（路径参数）

**请求体**：
```json
{
  "device_info": "OpenHarmony 3.0",  // 可选，设备信息
  "source": "web"                    // 可选，下载来源
}
```

**响应**：
```json
{
  "code": 200,
  "message": "下载记录成功",
  "data": {
    "download_id": 12345,
    "app_id": 1,
    "version_id": 1,
    "download_count": 1001,
    "downloaded_at": "2023-10-01T12:00:00Z"
  }
}
```

**说明**：
- 此接口无需认证，任何用户都可以访问
- 只能下载状态为"approved"且已发布的应用版本
- 会自动更新应用和版本的下载计数
- 未登录用户的UserID会被设置为0

### 获取应用列表

**请求**：
```
GET /api/v1/apps
```

**参数**：
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20
- `category`: 应用分类
- `sort`: 排序方式(newest/popular/rating)
- `keyword`: 搜索关键词

**响应**：
```json
{  "code": 200,
m  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "created_at": "2023-09-01T00:00:00Z",
        "updated_at": "2023-09-15T00:00:00Z",
        "deleted_at": null,
        "name": "Calculator Pro",
        "package_name": "com.example.calculator",
        "description": "A professional calculator app for OpenHarmony",
        "short_description": "Professional calculator",
        "icon": "https://example.com/app-icon.jpg",
        "category_id": 1,
        "category_name": "Tools",
        "developer_id": 1,
        "developer_name": "John Developer",
        "version": "1.0.0",
        "version_code": 100,
        "min_sdk_version": 21,
        "target_sdk_version": 33,
        "size": 2048000,
        "download_url": "https://example.com/calculator.apk",
        "download_count": 1000,
        "rating": 4.5,
        "review_count": 200,
        "screenshots": [
          "https://example.com/screenshot1.jpg",
          "https://example.com/screenshot2.jpg"
        ],
        "permissions": [
          "android.permission.INTERNET",
          "android.permission.CAMERA"
        ],
        "tags": ["calculator", "math", "tools"],
        "changelog": "Initial release with basic calculator functions",
        "privacy_policy": "https://example.com/privacy",
        "support_email": "<EMAIL>",
        "website": "https://example.com",
        "status": "published",
        "is_featured": false,
        "is_editor_choice": true,
        "is_top": false,
        "featured_at": null,
        "published_at": "2023-09-15T00:00:00Z",
        "review_status": "approved",
        "review_reason": "",
        "reviewed_at": "2023-09-14T00:00:00Z",
        "reviewer_id": 2
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 100,
      "total_pages": 5
    }
  }
}
```

### 获取应用详情

**请求**：
```
GET /api/v1/apps/{id}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "created_at": "2023-09-01T00:00:00Z",
    "updated_at": "2023-09-15T00:00:00Z",
    "deleted_at": null,
    "name": "Calculator Pro",
    "package_name": "com.example.calculator",
    "description": "A professional calculator app for OpenHarmony with scientific features.",
    "short_description": "Professional calculator",
    "icon": "https://example.com/app-icon.jpg",
    "category_id": 1,
    "category_name": "Tools",
    "developer_id": 1,
    "developer_name": "John Developer",
    "version": "1.0.0",
    "version_code": 100,
    "min_sdk_version": 21,
    "target_sdk_version": 33,
    "size": 2048000,
    "download_url": "https://example.com/calculator.apk",
    "download_count": 1000,
    "rating": 4.5,
    "review_count": 200,
    "permissions": [
      "android.permission.INTERNET",
      "android.permission.CAMERA"
    ],
    "tags": ["calculator", "math", "tools"],
    "changelog": "A professional calculator app with scientific features.",
    "privacy_policy": "https://calculator-pro.example.com/privacy",
    "support_email": "<EMAIL>",
    "website": "https://calculator-pro.example.com",
    "status": "published",
    "is_featured": false,
    "is_editor_choice": true,
    "is_top": false,
    "featured_at": null,
    "published_at": "2023-09-15T00:00:00Z",
    "review_status": "approved",
    "review_reason": "",
    "reviewed_at": "2023-09-14T00:00:00Z",
    "reviewer_id": 2,
    "screenshots": [
      {
        "id": 1,
        "application_id": 1,
        "image_url": "https://example.com/screenshot1.jpg",
        "sort_order": 1,
        "created_at": "2023-09-01T00:00:00Z"
      },
      {
        "id": 2,
        "application_id": 1,
        "image_url": "https://example.com/screenshot2.jpg",
        "sort_order": 2,
        "created_at": "2023-09-01T00:00:00Z"
      }
    ],
    "versions": [
      {
        "id": 1,
        "application_id": 1,
        "version_name": "1.0.0",
        "version_code": 100,
        "change_log": "Initial release",
        "package_url": "https://example.com/calculator-1.0.0.hap",
        "size": 2048000,
        "status": "approved",
        "min_open_harmony_os_ver": "3.0.0",
        "released_at": "2023-09-15T00:00:00Z",
        "download_count": 1000,
        "incremental_update": false,
        "created_at": "2023-09-01T00:00:00Z",
        "updated_at": "2023-09-15T00:00:00Z"
      }
    ]
  }
}
```

### 创建应用

**请求**：
```
POST /api/v1/apps
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "name": "Calculator Pro",       // 必填，2-100字符
  "package_name": "com.example.calculator", // 必填，3-100字符
  "description": "A professional calculator app for OpenHarmony with scientific features.", // 可选，应用详细描述
  "short_description": "Professional calculator", // 可选，最多200字符
  "category_id": 1,               // 必填，应用分类ID
  "icon": "https://example.com/app-icon.jpg", // 必填，应用图标URL
  "version": "1.0.0",             // 必填，版本号
  "version_code": 100,            // 必填，版本代码
  "min_sdk_version": 21,          // 必填，最低SDK版本
  "target_sdk_version": 33,       // 必填，目标SDK版本
  "download_url": "https://example.com/calculator.apk", // 必填，下载链接
  "size": 2048000,                // 必填，应用大小（字节）
  "permissions": [                // 可选，权限列表
    "android.permission.INTERNET",
    "android.permission.CAMERA"
  ],
  "tags": ["calculator", "math", "tools"], // 可选，标签数组
  "changelog": "Initial release with basic calculator functions", // 可选，更新日志
  "privacy_policy": "https://example.com/privacy", // 可选，隐私政策URL
  "support_email": "<EMAIL>", // 可选，支持邮箱
  "website": "https://example.com" // 可选，应用网站URL
}
```

**响应**：
```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 1,
    "created_at": "2023-10-01T12:00:00Z",
    "updated_at": "2023-10-01T12:00:00Z",
    "deleted_at": null,
    "name": "Calculator Pro",
    "package_name": "com.example.calculator",
    "description": "A professional calculator app for OpenHarmony with scientific features.",
    "short_description": "Professional calculator",
    "icon": "https://example.com/app-icon.jpg",
    "category_id": 1,
    "category_name": "Tools",
    "developer_id": 1,
    "developer_name": "John Developer",
    "version": "1.0.0",
    "version_code": 100,
    "min_sdk_version": 21,
    "target_sdk_version": 33,
    "size": 2048000,
    "download_url": "https://example.com/calculator.apk",
    "download_count": 0,
    "rating": 0,
    "review_count": 0,
    "screenshots": [],
    "permissions": [
      "android.permission.INTERNET",
      "android.permission.CAMERA"
    ],
    "tags": ["calculator", "math", "tools"],
    "changelog": "Initial release with basic calculator functions",
    "privacy_policy": "https://example.com/privacy",
    "support_email": "<EMAIL>",
    "website": "https://example.com",
    "status": "draft",
    "is_featured": false,
    "is_editor_choice": false,
    "is_top": false,
    "featured_at": null,
    "published_at": null,
    "review_status": "pending",
    "review_reason": "",
    "reviewed_at": null,
    "reviewer_id": null
  }
}
```

### 更新应用

**请求**：
```
PUT /api/v1/apps/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "name": "Calculator Pro Plus",  // 可选，2-100字符
  "description": "Updated description", // 可选，应用详细描述
  "short_description": "Professional calculator with more features", // 可选，最多200字符
  "category_id": 2,               // 可选，应用分类ID
  "icon": "https://example.com/new-icon.jpg", // 可选，应用图标URL
  "version": "1.1.0",             // 可选，版本号
  "version_code": 110,            // 可选，版本代码
  "min_sdk_version": 23,          // 可选，最低SDK版本
  "target_sdk_version": 34,       // 可选，目标SDK版本
  "download_url": "https://example.com/calculator-plus.apk", // 可选，下载链接
  "size": 2560000,                // 可选，应用大小（字节）
  "permissions": [                // 可选，权限列表
    "android.permission.INTERNET",
    "android.permission.CAMERA",
    "android.permission.WRITE_EXTERNAL_STORAGE"
  ],
  "tags": ["calculator", "math", "tools", "utilities"], // 可选，标签数组
  "changelog": "Added scientific calculator features", // 可选，更新日志
  "privacy_policy": "https://calculator-pro-plus.example.com/privacy", // 可选，隐私政策URL
  "support_email": "<EMAIL>", // 可选，支持邮箱
  "website": "https://calculator-pro-plus.example.com" // 可选，应用网站URL
}
```

**响应**：
```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": 1,
    "created_at": "2023-09-01T00:00:00Z",
    "updated_at": "2023-10-01T12:00:00Z",
    "deleted_at": null,
    "name": "Calculator Pro Plus",
    "package_name": "com.example.calculator",
    "description": "Updated description",
    "short_description": "Professional calculator with more features",
    "icon": "https://example.com/new-icon.jpg",
    "category_id": 2,
    "category_name": "Utilities",
    "developer_id": 1,
    "developer_name": "John Developer",
    "version": "1.1.0",
    "version_code": 110,
    "min_sdk_version": 23,
    "target_sdk_version": 34,
    "size": 2560000,
    "download_url": "https://example.com/calculator-plus.apk",
    "download_count": 1000,
    "rating": 4.5,
    "review_count": 200,
    "screenshots": [
      "https://example.com/screenshot1.jpg",
      "https://example.com/screenshot2.jpg"
    ],
    "permissions": [
      "android.permission.INTERNET",
      "android.permission.CAMERA",
      "android.permission.WRITE_EXTERNAL_STORAGE"
    ],
    "tags": ["calculator", "math", "tools", "utilities"],
    "changelog": "Added scientific calculator features",
    "privacy_policy": "https://calculator-pro-plus.example.com/privacy",
    "support_email": "<EMAIL>",
    "website": "https://calculator-pro-plus.example.com",
    "status": "published",
    "is_featured": false,
    "is_editor_choice": true,
    "is_top": false,
    "featured_at": null,
    "published_at": "2023-09-15T00:00:00Z",
    "review_status": "approved",
    "review_reason": "",
    "reviewed_at": "2023-09-14T00:00:00Z",
    "reviewer_id": 2
  }
}
``` 


## 精选集模块

精选集模块提供了管理和展示应用精选集的功能，包括创建、编辑、删除精选集以及管理精选集中的应用。

### 公开接口

#### 获取精选集列表

**请求**：
```
GET /api/v1/public/featured-collections
```

**参数**：
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20，最大100
- `status`: 状态过滤，可选值：active（默认）

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "编辑精选",
        "description": "编辑团队精心挑选的优质应用",
        "cover_image": "https://example.com/cover1.jpg",
        "app_count": 12,
        "display_order": 1,
        "status": "active",
        "created_at": "2023-09-01T00:00:00Z",
        "updated_at": "2023-09-15T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 5,
      "total_pages": 1
    }
  }
}
```

**说明**：
- 此接口无需认证，任何用户都可以访问
- 只返回状态为"active"的精选集
- 按display_order升序排列

#### 获取精选集详情

**请求**：
```
GET /api/v1/public/featured-collections/{id}
```

**参数**：
- `id`: 精选集ID（路径参数）

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "name": "编辑精选",
    "description": "编辑团队精心挑选的优质应用",
    "cover_image": "https://example.com/cover1.jpg",
    "app_count": 12,
    "display_order": 1,
    "status": "active",
    "created_at": "2023-09-01T00:00:00Z",
    "updated_at": "2023-09-15T00:00:00Z"
  }
}
```

**说明**：
- 此接口无需认证，任何用户都可以访问
- 只能获取状态为"active"的精选集详情
- 如果精选集不存在或状态不为"active"，返回404错误

#### 获取精选集中的应用列表

**请求**：
```
GET /api/v1/public/featured-collections/{id}/apps
```

**参数**：
- `id`: 精选集ID（路径参数）
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20，最大100

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "Calculator Pro",
        "package_name": "com.example.calculator",
        "description": "A professional calculator app for OpenHarmony",
        "short_description": "Professional calculator",
        "icon": "https://example.com/app-icon.jpg",
        "category_id": 1,
        "category_name": "Tools",
        "developer_id": 1,
        "developer_name": "John Developer",
        "version": "1.0.0",
        "size": 2048000,
        "download_count": 1000,
        "rating": 4.5,
        "review_count": 200,
        "tags": ["calculator", "math", "tools"],
        "status": "published",
        "published_at": "2023-09-15T00:00:00Z",
        "added_at": "2023-09-20T00:00:00Z",
        "display_order": 1
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 12,
      "total_pages": 1
    }
  }
}
```

**说明**：
- 此接口无需认证，任何用户都可以访问
- 只返回状态为"published"的应用
- 按精选集中的display_order升序排列
- 如果精选集不存在或状态不为"active"，返回404错误

### 管理员接口

#### 获取精选集列表（管理员）

**请求**：
```
GET /api/v1/admin/featured-collections
```

**请求头**：
```
Authorization: Bearer {token}
```

**参数**：
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20，最大100
- `status`: 状态过滤，可选值：active、inactive、all（默认）
- `keyword`: 搜索关键词，可选

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "编辑精选",
        "description": "编辑团队精心挑选的优质应用",
        "cover_image": "https://example.com/cover1.jpg",
        "app_count": 12,
        "display_order": 1,
        "status": "active",
        "created_at": "2023-09-01T00:00:00Z",
        "updated_at": "2023-09-15T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 8,
      "total_pages": 1
    }
  }
}
```

**说明**：
- 需要管理员权限
- 可以查看所有状态的精选集
- 支持关键词搜索和状态过滤

#### 创建精选集

**请求**：
```
POST /api/v1/admin/featured-collections
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "name": "新游戏精选",                    // 必填，2-100字符
  "description": "最新最热门的游戏合集",    // 必填，10-500字符
  "cover_image": "https://example.com/cover.jpg", // 可选，封面图片URL
  "display_order": 1,                   // 可选，显示顺序，默认0
  "status": "active"                    // 可选，状态，默认active
}
```

**响应**：
```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 2,
    "name": "新游戏精选",
    "description": "最新最热门的游戏合集",
    "cover_image": "https://example.com/cover.jpg",
    "app_count": 0,
    "display_order": 1,
    "status": "active",
    "created_at": "2023-10-01T12:00:00Z",
    "updated_at": "2023-10-01T12:00:00Z"
  }
}
```

#### 更新精选集

**请求**：
```
PUT /api/v1/admin/featured-collections/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "name": "更新后的精选集名称",           // 可选，2-100字符
  "description": "更新后的描述",        // 可选，10-500字符
  "cover_image": "https://example.com/new-cover.jpg", // 可选，封面图片URL
  "display_order": 2,                 // 可选，显示顺序
  "status": "inactive"                // 可选，状态
}
```

**响应**：
```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": 1,
    "name": "更新后的精选集名称",
    "description": "更新后的描述",
    "cover_image": "https://example.com/new-cover.jpg",
    "app_count": 12,
    "display_order": 2,
    "status": "inactive",
    "created_at": "2023-09-01T00:00:00Z",
    "updated_at": "2023-10-01T12:00:00Z"
  }
}
```

#### 删除精选集

**请求**：
```
DELETE /api/v1/admin/featured-collections/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

**说明**：
- 删除精选集会同时删除该精选集中的所有应用关联关系
- 删除操作不可逆，请谨慎操作

#### 批量删除精选集

**请求**：
```
DELETE /api/v1/admin/featured-collections
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "ids": [1, 2, 3]  // 必填，精选集ID数组
}
```

**响应**：
```json
{
  "code": 200,
  "message": "批量删除成功",
  "data": {
    "deleted_count": 3,
    "failed_ids": []
  }
}
```

#### 更新精选集状态

**请求**：
```
PUT /api/v1/admin/featured-collections/{id}/status
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "status": "inactive"  // 必填，状态：active或inactive
}
```

**响应**：
```json
{
  "code": 200,
  "message": "状态更新成功",
  "data": null
}
```

#### 更新精选集显示顺序

**请求**：
```
PUT /api/v1/admin/featured-collections/order
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "orders": [
    {"id": 1, "display_order": 1},
    {"id": 2, "display_order": 2},
    {"id": 3, "display_order": 3}
  ]
}
```

**响应**：
```json
{
  "code": 200,
  "message": "顺序更新成功",
  "data": null
}
```

#### 获取精选集中的应用列表（管理员）

**请求**：
```
GET /api/v1/admin/featured-collections/{id}/apps
```

**请求头**：
```
Authorization: Bearer {token}
```

**参数**：
- `id`: 精选集ID（路径参数）
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20，最大100

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "Calculator Pro",
        "package_name": "com.example.calculator",
        "icon": "https://example.com/app-icon.jpg",
        "category_name": "Tools",
        "developer_name": "John Developer",
        "version": "1.0.0",
        "download_count": 1000,
        "rating": 4.5,
        "status": "published",
        "added_at": "2023-09-20T00:00:00Z",
        "display_order": 1
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 12,
      "total_pages": 1
    }
  }
}
```

#### 向精选集添加应用

**请求**：
```
POST /api/v1/admin/featured-collections/{id}/apps
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "app_ids": [1, 2, 3],      // 必填，应用ID数组
  "display_order": 1         // 可选，显示顺序，默认为当前最大值+1
}
```

**响应**：
```json
{
  "code": 200,
  "message": "添加成功",
  "data": {
    "added_count": 3,
    "failed_app_ids": [],
    "duplicate_app_ids": []
  }
}
```

#### 从精选集移除应用

**请求**：
```
DELETE /api/v1/admin/featured-collections/{id}/apps
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "app_ids": [1, 2, 3]  // 必填，应用ID数组
}
```

**响应**：
```json
{
  "code": 200,
  "message": "移除成功",
  "data": {
    "removed_count": 3,
    "failed_app_ids": []
  }
}
```

## 评论模块

### 获取应用评论

**请求**：
```
GET /api/v1/public/apps/{id}/reviews
```

**参数**：
- 应用ID (path)
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "application_id": 1,
      "app_name": "Calculator Pro",
      "user_id": 2,
      "username": "jane_user",
      "avatar": "https://example.com/user-avatar.jpg",
      "title": "Great calculator app",
      "content": "This is a very useful calculator with all the features I need.",
      "rating": 5,
      "app_version": "1.0.0",
      "like_count": 10,
      "dev_response": "Thank you for your feedback!",
      "status": "active",
      "created_at": "2023-09-20T00:00:00Z",
      "updated_at": "2023-09-21T00:00:00Z"
    }
  ],
  "total": 42,
  "page": 1,
  "page_size": 20
}
```

### 发表评论

**请求**：
```
POST /api/v1/apps/{id}/reviews
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "title": "Great calculator app",     // 必填，最多100字符
  "content": "This is a very useful calculator with all the features I need.", // 必填，1-1000字符
  "rating": 5,                         // 必填，1-5分
  "app_version": "1.0.0"               // 必填，应用版本，最多50字符
}
```

**响应**：
```json
{
  "code": 200,
  "message": "评论发表成功",
  "data": {
    "id": 1,
    "application_id": 1,
    "app_name": "Calculator Pro",
    "user_id": 2,
    "username": "jane_user",
    "avatar": "https://example.com/user-avatar.jpg",
    "title": "Great calculator app",
    "content": "This is a very useful calculator with all the features I need.",
    "rating": 5,
    "app_version": "1.0.0",
    "like_count": 0,
    "dev_response": "",
    "status": "active",
    "created_at": "2023-10-01T12:00:00Z",
    "updated_at": "2023-10-01T12:00:00Z"
  }
}
```

### 开发者回复评论

**请求**：
```
POST /api/v1/apps/{id}/reviews/{review_id}/respond
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "content": "Thank you for your feedback! We're glad you enjoy our app." // 必填，1-1000字符
}
```

**响应**：
```json
{
  "code": 200,
  "message": "回复成功",
  "data": {
    "id": 1,
    "application_id": 1,
    "app_name": "Calculator Pro",
    "user_id": 2,
    "username": "jane_user",
    "avatar": "https://example.com/user-avatar.jpg",
    "title": "Great calculator app",
    "content": "This is a very useful calculator with all the features I need.",
    "rating": 5,
    "app_version": "1.0.0",
    "like_count": 0,
    "dev_response": "Thank you for your feedback! We're glad you enjoy our app.",
    "status": "active",
    "created_at": "2023-10-01T12:00:00Z",
    "updated_at": "2023-10-01T13:00:00Z"
  }
}
```

### 点赞评论

**请求**：
```
POST /api/v1/apps/{id}/reviews/{review_id}/like
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "点赞成功",
  "data": null
}
```

### 取消点赞评论

**请求**：
```
POST /api/v1/apps/{id}/reviews/{review_id}/unlike
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "取消点赞成功",
  "data": null
}
```

## 统计模块

### 获取应用下载统计

**请求**：
```
GET /api/v1/stats/apps/{id}/downloads
```

**请求头**：
```
Authorization: Bearer {token}
```

**参数**：
- 应用ID (path)
- `days`: 统计天数，默认30

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total_downloads": 5280,
    "daily_stats": [
      {"date": "2023-09-01", "downloads": 120},
      {"date": "2023-09-02", "downloads": 135},
      {"date": "2023-09-03", "downloads": 150}
      // ...更多日期数据
    ],
    "device_stats": [
      {"device_type": "phone", "downloads": 4224},
      {"device_type": "tablet", "downloads": 1056}
    ]
  }
}
```

### 获取用户下载记录

**请求**：
```
GET /api/v1/stats/downloads
```

**请求头**：
```
Authorization: Bearer {token}
```

**参数**：
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "user_id": 2,
      "application_id": 1,
      "app_name": "Calculator Pro",
      "app_icon": "https://example.com/app-icon.jpg",
      "version_name": "1.0.0",
      "device_type": "phone",
      "device_model": "Mate 40 Pro",
      "device_os": "HarmonyOS 3.0.0",
      "status": "completed",
      "created_at": "2023-09-15T10:30:00Z"
    }
  ],
  "total": 15,
  "page": 1,
  "page_size": 20
}
```

### 记录应用下载

**请求**：
```
POST /api/v1/apps/{id}/versions/{version_id}/download
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "device_type": "phone",        // 设备类型
  "device_model": "Mate 40 Pro", // 设备型号
  "device_os": "HarmonyOS 3.0.0" // 设备操作系统版本
}
```

**响应**：
```json
{
  "code": 200,
  "message": "下载记录成功",
  "data": {
    "id": 1,
    "user_id": 2,
    "application_id": 1,
    "version_id": 1,
    "device_type": "phone",
    "device_model": "Mate 40 Pro",
    "device_os": "HarmonyOS 3.0.0",
    "status": "completed",
    "created_at": "2023-10-01T12:00:00Z"
  }
}
``` 


## 审核员模块

### 获取待审核应用

**请求**：
```
GET /api/v1/reviewer/apps/pending
```

**请求头**：
```
Authorization: Bearer {token}
```

**参数**：
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 3,
      "name": "Weather App",
      "package": "com.example.weather",
      "description": "A weather forecasting app for OpenHarmony.",
      "short_desc": "Weather forecasting",
      "icon": "https://example.com/weather-icon.jpg",
      "category": "Tools",
      "developer_id": 1,
      "developer_name": "John Developer",
      "status": "pending",
      "min_open_harmony_os_ver": "3.0.0",
      "submitted_at": "2023-09-25T08:45:00Z"
    }
  ],
  "total": 5,
  "page": 1,
  "page_size": 20
}
```

### 审核应用

**请求**：
```
POST /api/v1/reviewer/apps/{id}/review
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "status": "approved",               // 必填，审核结果(approved/rejected)
  "reason": "应用符合商店发布规范"  // 拒绝时必填，拒绝理由
}
```

**响应**：
```json
{
  "code": 200,
  "message": "审核成功",
  "data": {
    "id": 3,
    "name": "Weather App",
    "package": "com.example.weather",
    "status": "approved",
    "reviewed_by": 5,
    "reviewed_at": "2023-10-01T12:00:00Z",
    "review_reason": "应用符合商店发布规范"
  }
}
```

## 运营人员模块

### 设置应用推荐状态

**请求**：
```
PUT /api/v1/operator/apps/{id}/featured
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "is_featured": true,        // 必填，是否推荐
  "featured_reason": "高质量应用，推荐给用户" // 可选，推荐理由
}
```

**响应**：
```json
{
  "code": 200,
  "message": "设置成功",
  "data": {
    "id": 1,
    "name": "Calculator Pro",
    "is_featured": true,
    "featured_at": "2023-10-01T12:00:00Z",
    "featured_by": 4,
    "featured_reason": "高质量应用，推荐给用户"
  }
}
```

### 设置应用编辑精选状态

**请求**：
```
PUT /api/v1/operator/apps/{id}/editor
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "is_editor": true,          // 必填，是否编辑精选
  "editor_comment": "编辑推荐，功能全面的计算器应用" // 可选，编辑评语
}
```

**响应**：
```json
{
  "code": 200,
  "message": "设置成功",
  "data": {
    "id": 1,
    "name": "Calculator Pro",
    "is_editor": true,
    "editor_at": "2023-10-01T12:00:00Z",
    "editor_by": 4,
    "editor_comment": "编辑推荐，功能全面的计算器应用"
  }
}
```

### 设置应用置顶状态

**请求**：
```
PUT /api/v1/operator/apps/{id}/top
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "is_top": true,            // 必填，是否置顶
  "top_order": 1,            // 可选，置顶排序，数字越小越靠前
  "top_reason": "精品应用，值得置顶推荐" // 可选，置顶理由
}
```

**响应**：
```json
{
  "code": 200,
  "message": "设置成功",
  "data": {
    "id": 1,
    "name": "Calculator Pro",
    "is_top": true,
    "top_order": 1,
    "top_at": "2023-10-01T12:00:00Z",
    "top_by": 4,
    "top_reason": "精品应用，值得置顶推荐"
  }
}
```

## 系统通用接口

### 健康检查

**请求**：
```
GET /api/v1/health
```

**响应**：
```json
{
  "code": 200,
  "message": "服务正常",
  "data": {
    "status": "ok",
    "time": "2023-10-01T12:00:00Z",
    "version": "1.0.0"
  }
}
```

## 分类管理模块

### 获取应用分类

**请求**：
```
GET /api/v1/public/categories/list
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "created_at": "2023-09-01T12:00:00Z",
      "updated_at": "2023-09-01T12:00:00Z",
      "deleted_at": null,
      "name": "工具",
      "description": "实用工具类应用",
      "icon": "https://example.com/category-tools.png",
      "sort_order": 1,
      "is_active": true,
      "parent_id": null
    },
    {
      "id": 2,
      "created_at": "2023-09-01T12:00:00Z",
      "updated_at": "2023-09-01T12:00:00Z",
      "deleted_at": null,
      "name": "社交",
      "description": "社交通讯类应用",
      "icon": "https://example.com/category-social.png",
      "sort_order": 2,
      "is_active": true,
      "parent_id": null
    },
    {
      "id": 3,
      "created_at": "2023-09-01T12:00:00Z",
      "updated_at": "2023-09-01T12:00:00Z",
      "deleted_at": null,
      "name": "游戏",
      "description": "游戏娱乐类应用",
      "icon": "https://example.com/category-games.png",
      "sort_order": 3,
      "is_active": true,
      "parent_id": null
    }
  ]
}
```

### 创建分类（管理员）

**请求**：
```
POST /api/v1/admin/categories
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "name": "教育",                    // 必填，最多50字符
  "description": "教育学习类应用",    // 可选，最多1000字符
  "icon": "https://example.com/category-education.png", // 可选，图标URL
  "sort_order": 4,                 // 可选，排序权重，默认0
  "parent_id": null                // 可选，父分类ID，支持多级分类
}
```

**响应**：
```json
{
  "code": 201,
  "message": "创建成功",
  "data": {
    "id": 4,
    "created_at": "2023-10-01T12:00:00Z",
    "updated_at": "2023-10-01T12:00:00Z",
    "deleted_at": null,
    "name": "教育",
    "description": "教育学习类应用",
    "icon": "https://example.com/category-education.png",
    "sort_order": 4,
    "is_active": true,
    "parent_id": null
  }
}
```

### 更新分类（管理员）

**请求**：
```
PUT /api/v1/admin/categories/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "name": "教育学习",               // 可选，最多50字符
  "description": "教育学习类应用",   // 可选，最多1000字符
  "icon": "https://example.com/category-education-new.png", // 可选，图标URL
  "sort_order": 5,                // 可选，排序权重
  "is_active": true,              // 可选，是否启用
  "parent_id": null               // 可选，父分类ID
}
```

**响应**：
```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": 4,
    "created_at": "2023-10-01T12:00:00Z",
    "updated_at": "2023-10-01T13:00:00Z",
    "deleted_at": null,
    "name": "教育学习",
    "description": "教育学习类应用",
    "icon": "https://example.com/category-education-new.png",
    "sort_order": 5,
    "is_active": true,
    "parent_id": null
  }
}
```

### 删除分类（管理员）

**请求**：
```
DELETE /api/v1/admin/categories/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

### 获取标签列表

**请求**：
```
GET /api/v1/public/tags
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "计算器",
      "name_en": "calculator",
      "use_count": 15
    },
    {
      "id": 2,
      "name": "工具",
      "name_en": "tools",
      "use_count": 120
    },
    {
      "id": 3,
      "name": "效率",
      "name_en": "productivity",
      "use_count": 85
    }
  ]
}
```

## 用户退出登录

### 请求

```
POST /api/v1/users/logout
```

### 请求头

```
Content-Type: application/json
Authorization: Bearer {token}
```

### 响应

```
{
    "code": 200,
    "message": "登出成功",
    "data": null
}
```

## 开发者认证模块

### 提交开发者认证

**请求**：
```
POST /api/v1/developers/verify
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "developer_name": "张三",
  "company_name": "北京科技有限公司",
  "website": "https://example.com",
  "description": "专注于移动应用开发",
  "contact_email": "<EMAIL>",
  "contact_phone": "***********",
  "business_license": "https://example.com/license.jpg",
  "id_card_front": "https://example.com/id_front.jpg",
  "id_card_back": "https://example.com/id_back.jpg"
}
```

**响应**：
```json
{
  "code": 200,
  "message": "认证申请提交成功",
  "data": null
}
```

### 获取开发者认证状态

**请求**：
```
GET /api/v1/developers/verify/status
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "status": "pending",
    "submitted_at": "2023-09-01T12:00:00Z",
    "reviewed_at": null,
    "reject_reason": null
  }
}
```

## 统计模块

### 获取用户下载记录

**请求**：
```
GET /api/v1/stats/downloads?page=1&page_size=20
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "application_id": 1,
      "app_version_id": 1,
      "version_name": "1.0.0",
      "downloaded_at": "2023-09-01T12:00:00Z",
      "device_type": "phone",
      "device_os": "HarmonyOS 4.0",
      "status": "success"
    }
  ],
  "total": 50,
  "page": 1,
  "page_size": 20
}
```

### 获取应用下载统计

**请求**：
```
GET /api/v1/stats/apps/{id}/downloads?days=30
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total_downloads": 1500,
    "daily_stats": [
      {
        "date": "2023-09-30",
        "downloads": 45
      },
      {
        "date": "2023-09-29",
        "downloads": 38
      }
    ]
  }
}
```

## 管理员模块

### 开发者管理

#### 获取开发者列表

**请求**：
```
GET /api/v1/admin/developers?page=1&page_size=20&status=pending
```

**请求头**：
```
Authorization: Bearer {token}
```

**查询参数**：
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20
- `status`: 认证状态过滤 (pending/approved/rejected)

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "user_id": 10,
      "username": "developer1",
      "developer_name": "张三",
      "company_name": "北京科技有限公司",
      "website": "https://example.com",
      "description": "专注于移动应用开发",
      "contact_email": "<EMAIL>",
      "contact_phone": "***********",
      "business_license": "https://example.com/license.jpg",
      "identity_card": "https://example.com/id_card.jpg",
      "developer_avatar": "https://example.com/avatar.jpg",
      "developer_address": "北京市朝阳区",
      "verify_status": "pending",
      "submitted_at": "2023-09-01T12:00:00Z",
      "verified_at": null,
      "verify_reason": ""
    }
  ],
  "total": 5,
  "page": 1,
  "page_size": 20
}
```

#### 审核开发者认证

**请求**：
```
POST /api/v1/admin/developers/{id}/verify
```

**请求头**：
```
Authorization: Bearer {token}
```

**路径参数**：
- `id`: 用户ID

**请求体**：
```json
{
  "verify_status": "approved",  // approved 或 rejected
  "verify_reason": "审核通过，资料完整"    // 审核理由，拒绝时必填
}
```

**响应**：
```json
{
  "code": 200,
  "message": "审核完成",
  "data": {
    "id": 1,
    "user_id": 10,
    "developer_name": "张三",
    "verify_status": "approved",
    "verify_reason": "审核通过，资料完整",
    "verified_at": "2023-09-30T12:00:00Z"
  }
}
```

#### 获取最近开发者申请

**请求**：
```
GET /api/v1/admin/developers/recent?limit=10
```

**请求头**：
```
Authorization: Bearer {token}
```

**查询参数**：
- `limit`: 返回数量限制，默认10

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "user_id": 10,
      "developer_name": "张三",
      "company_name": "北京科技有限公司",
      "verify_status": "pending",
      "submitted_at": "2023-09-30T12:00:00Z"
    }
  ]
}
```

### 获取用户列表

**请求**：
```
GET /api/v1/admin/users?page=1&page_size=20&role=&status=&keyword=
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "username": "user1",
      "email": "<EMAIL>",
      "phone": "***********",
      "role": "user",
      "status": "active",
      "is_developer": false,
      "created_at": "2023-09-01T12:00:00Z",
      "last_login_at": "2023-09-30T12:00:00Z"
    }
  ],
  "total": 100,
  "page": 1,
  "page_size": 20
}
```

### 创建用户

**请求**：
```
POST /api/v1/admin/users
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "username": "newuser",
  "email": "<EMAIL>",
  "phone": "***********",
  "password": "password123",
  "role": "user",
  "status": "active"
}
```

**响应**：
```json
{
  "code": 200,
  "message": "用户创建成功",
  "data": {
    "id": 101,
    "username": "newuser",
    "email": "<EMAIL>",
    "role": "user",
    "status": "active"
  }
}
```

### 更新用户角色

**请求**：
```
PUT /api/v1/admin/users/{id}/role
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "role": "admin"  // user, admin, operator, reviewer
}
```

**响应**：
```json
{
  "code": 200,
  "message": "角色更新成功",
  "data": null
}
```

### 更新用户状态

**请求**：
```
PUT /api/v1/admin/users/{id}/status
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "status": "banned"  // active, inactive, banned
}
```

**响应**：
```json
{
  "code": 200,
  "message": "状态更新成功",
  "data": null
}
```

### 帮助中心管理

#### 创建帮助分类

**请求**：
```
POST /api/v1/admin/help/categories
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "name": "常见问题",
  "description": "用户常见问题解答",
  "sort_order": 1,
  "is_active": true
}
```

**响应**：
```json
{
  "code": 200,
  "message": "分类创建成功",
  "data": {
    "id": 1,
    "name": "常见问题",
    "description": "用户常见问题解答",
    "sort_order": 1,
    "is_active": true,
    "created_at": "2023-09-30T12:00:00Z"
  }
}
```

#### 更新帮助分类

**请求**：
```
PUT /api/v1/admin/help/categories/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "name": "常见问题FAQ",
  "description": "更新后的描述",
  "sort_order": 2,
  "is_active": false
}
```

**响应**：
```json
{
  "code": 200,
  "message": "分类更新成功",
  "data": null
}
```

#### 删除帮助分类

**请求**：
```
DELETE /api/v1/admin/help/categories/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "分类删除成功",
  "data": null
}
```

#### 获取帮助分类列表（管理员）

**请求**：
```
GET /api/v1/admin/help/categories?page=1&page_size=20
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "常见问题",
      "description": "用户常见问题解答",
      "sort_order": 1,
      "is_active": true,
      "article_count": 5,
      "created_at": "2023-09-30T12:00:00Z"
    }
  ],
  "total": 10,
  "page": 1,
  "page_size": 20
}
```

#### 创建帮助文章

**请求**：
```
POST /api/v1/admin/help/articles
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "category_id": 1,
  "title": "如何注册账号",
  "content": "详细的注册步骤说明...",
  "sort_order": 1,
  "is_published": true,
  "tags": ["注册", "账号"]
}
```

**响应**：
```json
{
  "code": 200,
  "message": "文章创建成功",
  "data": {
    "id": 1,
    "category_id": 1,
    "title": "如何注册账号",
    "content": "详细的注册步骤说明...",
    "sort_order": 1,
    "is_published": true,
    "view_count": 0,
    "created_at": "2023-09-30T12:00:00Z"
  }
}
```

#### 更新帮助文章

**请求**：
```
PUT /api/v1/admin/help/articles/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "title": "如何注册和激活账号",
  "content": "更新后的注册步骤说明...",
  "is_published": false
}
```

**响应**：
```json
{
  "code": 200,
  "message": "文章更新成功",
  "data": null
}
```

#### 删除帮助文章

**请求**：
```
DELETE /api/v1/admin/help/articles/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "文章删除成功",
  "data": null
}
```

#### 获取帮助文章列表（管理员）

**请求**：
```
GET /api/v1/admin/help/articles?page=1&page_size=20&category_id=1&published=true
```

**请求头**：
```
Authorization: Bearer {token}
```

**查询参数**：
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20
- `category_id`: 分类ID过滤
- `published`: 发布状态过滤

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "category_id": 1,
      "category_name": "常见问题",
      "title": "如何注册账号",
      "content": "详细的注册步骤说明...",
      "sort_order": 1,
      "is_published": true,
      "view_count": 150,
      "created_at": "2023-09-30T12:00:00Z",
      "updated_at": "2023-09-30T12:00:00Z"
    }
  ],
  "total": 25,
  "page": 1,
  "page_size": 20
}
```

#### 获取帮助文章详情（管理员）

**请求**：
```
GET /api/v1/admin/help/articles/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "category_id": 1,
    "category_name": "常见问题",
    "title": "如何注册账号",
    "content": "详细的注册步骤说明...",
    "sort_order": 1,
    "is_published": true,
    "view_count": 150,
    "tags": ["注册", "账号"],
    "created_at": "2023-09-30T12:00:00Z",
    "updated_at": "2023-09-30T12:00:00Z"
  }
}
```

### 轮播图管理

#### 获取轮播图详情（管理员）

**请求**：
```
GET /api/v1/admin/carousels/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "title": "热门应用推荐",
    "subtitle": "发现最新最热门的应用",
    "image_url": "https://example.com/carousel1.jpg",
    "type": "app",
    "target_id": 123,
    "target_url": "",
    "status": "active",
    "sort_order": 1,
    "click_count": 1520,
    "start_time": "2024-01-01T00:00:00Z",
    "end_time": "2024-12-31T23:59:59Z",
    "created_at": "2023-09-30T12:00:00Z",
    "updated_at": "2023-09-30T12:00:00Z"
  }
}
```

### 意见反馈管理

#### 回复用户反馈

**请求**：
```
POST /api/v1/admin/feedback/{id}/reply
```

**请求头**：
```
Authorization: Bearer {token}
```

**路径参数**：
- `id`: 反馈ID

**请求体**：
```json
{
  "reply_content": "感谢您的反馈，我们已经记录了您的建议，将在下个版本中考虑实现。"
}
```

**响应**：
```json
{
  "code": 200,
  "message": "回复成功",
  "data": {
    "id": 1,
    "feedback_id": 1,
    "reply_content": "感谢您的反馈，我们已经记录了您的建议，将在下个版本中考虑实现。",
    "admin_id": 1,
    "admin_name": "管理员",
    "created_at": "2023-09-30T12:00:00Z"
  }
}
```

## OpenHarmony 版本管理模块

### 创建 OpenHarmony 版本

**请求**：
```
POST /api/v1/admin/harmony-versions
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "version_name": "OpenHarmony 4.0",
  "version_code": "4.0.0",
  "description": "OpenHarmony 4.0 正式版，带来全新的用户体验和性能提升",
  "is_active": true
}
```

**响应**：
```json
{
  "code": 200,
  "message": "版本创建成功",
  "data": {
    "id": 1,
    "version_name": "OpenHarmony 4.0",
    "version_code": "4.0.0",
    "description": "OpenHarmony 4.0 正式版，带来全新的用户体验和性能提升",
    "is_active": true,
    "created_at": "2023-09-30T12:00:00Z",
    "updated_at": "2023-09-30T12:00:00Z"
  }
}
```

### 获取 OpenHarmony 版本列表

**请求**：
```
GET /api/v1/admin/harmony-versions?page=1&page_size=20&active=true
```

**请求头**：
```
Authorization: Bearer {token}
```

**查询参数**：
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20
- `active`: 是否只显示激活的版本，可选

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "version_name": "OpenHarmony 4.0",
      "version_code": "4.0.0",
      "description": "OpenHarmony 4.0 正式版，带来全新的用户体验和性能提升",
      "is_active": true,
      "created_at": "2023-09-30T12:00:00Z",
      "updated_at": "2023-09-30T12:00:00Z"
    },
    {
      "id": 2,
      "version_name": "OpenHarmony 3.2",
      "version_code": "3.2.0",
      "description": "OpenHarmony 3.2 稳定版",
      "is_active": true,
      "created_at": "2023-08-15T12:00:00Z",
      "updated_at": "2023-08-15T12:00:00Z"
    }
  ],
  "total": 5,
  "page": 1,
  "page_size": 20
}
```

### 获取 OpenHarmony 版本详情

**请求**：
```
GET /api/v1/admin/harmony-versions/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**路径参数**：
- `id`: 版本ID

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "version_name": "OpenHarmony 4.0",
    "version_code": "4.0.0",
    "description": "OpenHarmony 4.0 正式版，带来全新的用户体验和性能提升",
    "is_active": true,
    "created_at": "2023-09-30T12:00:00Z",
    "updated_at": "2023-09-30T12:00:00Z"
  }
}
```

### 更新 OpenHarmony 版本

**请求**：
```
PUT /api/v1/admin/harmony-versions/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**路径参数**：
- `id`: 版本ID

**请求体**：
```json
{
  "version_name": "OpenHarmony 4.0.1",
  "version_code": "4.0.1",
  "description": "OpenHarmony 4.0.1 修复版，修复了已知问题",
  "is_active": false
}
```

**响应**：
```json
{
  "code": 200,
  "message": "版本更新成功",
  "data": {
    "id": 1,
    "version_name": "OpenHarmony 4.0.1",
    "version_code": "4.0.1",
    "description": "OpenHarmony 4.0.1 修复版，修复了已知问题",
    "is_active": false,
    "created_at": "2023-09-30T12:00:00Z",
    "updated_at": "2023-10-01T12:00:00Z"
  }
}
```

### 删除 OpenHarmony 版本

**请求**：
```
DELETE /api/v1/admin/harmony-versions/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**路径参数**：
- `id`: 版本ID

**响应**：
```json
{
  "code": 200,
  "message": "版本删除成功",
  "data": null
}
```

**说明**：
- 只有管理员可以管理 OpenHarmony 版本
- 删除版本前需要确保没有应用依赖该版本
- 建议使用停用而不是删除来管理版本

## 审核员模块

### 获取待审核应用列表

**请求**：
```
GET /api/v1/reviewer/apps/pending?page=1&page_size=20
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "计算器应用",
      "package_name": "com.example.calculator",
      "developer_name": "张三",
      "version": "1.0.0",
      "status": "pending",
      "submitted_at": "2023-09-01T12:00:00Z",
      "description": "一个简单的计算器应用"
    }
  ],
  "total": 10,
  "page": 1,
  "page_size": 20
}
```

### 审核应用

**请求**：
```
POST /api/v1/reviewer/apps/{id}/review
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "action": "approve",  // approve 或 reject
  "reason": "应用功能完善，符合上架要求"  // 审核理由
}
```

**响应**：
```json
{
  "code": 200,
  "message": "审核完成",
  "data": null
}
```

## 仪表盘模块

### 分析页 - 获取摘要数据

**请求**：
```
GET /api/v1/dashboard/analytics/summary
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total_apps": 1250,
    "total_users": 8500,
    "total_downloads": 125000,
    "total_reviews": 3200,
    "growth_rate": {
      "apps": 12.5,
      "users": 8.3,
      "downloads": 15.2,
      "reviews": 6.8
    }
  }
}
```

### 分析页 - 获取趋势数据

**请求**：
```
GET /api/v1/dashboard/analytics/trend?period=30d&metric=downloads
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "period": "30d",
    "metric": "downloads",
    "data": [
      {
        "date": "2023-09-01",
        "value": 1250
      },
      {
        "date": "2023-09-02",
        "value": 1380
      }
    ]
  }
}
```

### 分析页 - 获取分类统计

**请求**：
```
GET /api/v1/dashboard/analytics/categories
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "category_name": "工具",
      "app_count": 320,
      "download_count": 45000,
      "percentage": 25.6
    },
    {
      "category_name": "游戏",
      "app_count": 280,
      "download_count": 38000,
      "percentage": 22.4
    }
  ]
}
```

### 分析页 - 获取热门应用

**请求**：
```
GET /api/v1/dashboard/analytics/popular-apps?limit=10
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "计算器Pro",
      "package_name": "com.example.calculator",
      "download_count": 15000,
      "rating": 4.8,
      "category": "工具"
    }
  ]
}
```

### 监控页 - 获取监控数据

**请求**：
```
GET /api/v1/dashboard/monitoring/data
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "server_status": "healthy",
    "cpu_usage": 45.2,
    "memory_usage": 68.5,
    "disk_usage": 32.1,
    "active_users": 1250,
    "api_response_time": 120,
    "error_rate": 0.02
  }
}
```

### 监控页 - 获取系统日志

**请求**：
```
GET /api/v1/dashboard/monitoring/logs?level=error&page=1&page_size=50
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "level": "error",
      "message": "数据库连接超时",
      "source": "database",
      "timestamp": "2023-09-30T12:00:00Z",
      "details": {}
    }
  ],
  "total": 25,
  "page": 1,
  "page_size": 50
}
```

### 监控页 - 获取告警事件

**请求**：
```
GET /api/v1/dashboard/monitoring/alerts?status=active&page=1&page_size=20
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "title": "CPU使用率过高",
      "description": "服务器CPU使用率超过80%",
      "level": "warning",
      "status": "active",
      "created_at": "2023-09-30T12:00:00Z",
      "resolved_at": null
    }
  ],
  "total": 3,
  "page": 1,
  "page_size": 20
}
```

### 工作台 - 获取摘要数据

**请求**：
```
GET /api/v1/dashboard/workbench/summary
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "pending_tasks": 5,
    "completed_tasks": 12,
    "pending_reviews": 8,
    "new_messages": 3,
    "recent_activities": [
      {
        "type": "app_review",
        "description": "审核了应用《计算器Pro》",
        "timestamp": "2023-09-30T12:00:00Z"
      }
    ]
  }
}
```

### 工作台 - 获取最近活动

**请求**：
```
GET /api/v1/dashboard/workbench/activities?page=1&page_size=20
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "type": "app_review",
      "title": "应用审核",
      "description": "审核了应用《计算器Pro》",
      "user": "管理员",
      "timestamp": "2023-09-30T12:00:00Z"
    }
  ],
  "total": 50,
  "page": 1,
  "page_size": 20
}
```

### 工作台 - 获取任务列表

**请求**：
```
GET /api/v1/dashboard/workbench/tasks?status=pending&page=1&page_size=20
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "title": "审核新应用",
      "description": "审核开发者提交的新应用",
      "priority": "high",
      "status": "pending",
      "assignee": "审核员A",
      "due_date": "2023-10-01T18:00:00Z",
      "created_at": "2023-09-30T09:00:00Z"
    }
  ],
  "total": 15,
  "page": 1,
  "page_size": 20
}
```

### 工作台 - 创建任务

**请求**：
```
POST /api/v1/dashboard/workbench/tasks
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "title": "优化数据库性能",
  "description": "分析并优化数据库查询性能",
  "priority": "medium",
  "assignee": "开发工程师B",
  "due_date": "2023-10-05T18:00:00Z"
}
```

**响应**：
```json
{
  "code": 200,
  "message": "任务创建成功",
  "data": {
    "id": 16,
    "title": "优化数据库性能",
    "status": "pending",
    "created_at": "2023-09-30T14:00:00Z"
  }
}
```

### 工作台 - 更新任务

**请求**：
```
PUT /api/v1/dashboard/workbench/tasks/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "title": "优化数据库性能（已完成50%）",
  "status": "in_progress",
  "priority": "high"
}
```

**响应**：
```json
{
  "code": 200,
  "message": "任务更新成功",
  "data": null
}
```

### 工作台 - 删除任务

**请求**：
```
DELETE /api/v1/dashboard/workbench/tasks/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "任务删除成功",
  "data": null
}
```

## 搜索模块

### 搜索应用

**请求**：
```
GET /api/v1/public/search/apps
```

**参数**：
- `keyword`: 搜索关键词
- `category`: 分类ID
- `tags`: 标签（多个用逗号分隔）
- `min_rating`: 最低评分
- `max_rating`: 最高评分
- `sort`: 排序方式（relevance/rating/downloads/newest）
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20

**响应**：
```json
{
  "code": 200,
  "message": "搜索成功",
  "data": {
    "apps": [
      {
        "id": 1,
        "name": "Calculator Pro",
        "package_name": "com.example.calculator",
        "description": "A professional calculator app",
        "icon": "https://example.com/app-icon.jpg",
        "category_name": "Tools",
        "developer_name": "John Developer",
        "version": "1.0.0",
        "rating": 4.5,
        "download_count": 1000,
        "tags": ["calculator", "math", "tools"]
      }
    ],
    "total": 50,
    "page": 1,
    "page_size": 20
  }
}
```

### 获取搜索建议

**请求**：
```
GET /api/v1/public/search/suggestions?q=calc
```

**参数**：
- `q`: 搜索关键词（必填）

**响应**：
```json
{
  "code": 200,
  "message": "获取搜索建议成功",
  "data": {
    "suggestions": ["calculator", "calendar", "call recorder"]
  }
}
```

### 搜索评论

**请求**：
```
GET /api/v1/public/search/reviews
```

**参数**：
- `keyword`: 搜索关键词
- `app_id`: 应用ID
- `user_id`: 用户ID
- `min_rating`: 最低评分
- `max_rating`: 最高评分
- `start_date`: 开始日期（YYYY-MM-DD）
- `end_date`: 结束日期（YYYY-MM-DD）
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20

**响应**：
```json
{
  "code": 200,
  "message": "搜索成功",
  "data": {
    "reviews": [
      {
        "id": 1,
        "application_id": 1,
        "app_name": "Calculator Pro",
        "user_id": 2,
        "username": "jane_user",
        "title": "Great calculator app",
        "content": "This is a very useful calculator",
        "rating": 5,
        "created_at": "2023-09-20T00:00:00Z"
      }
    ],
    "total": 25,
    "page": 1,
    "page_size": 20
  }
}
```

### 搜索标签

**请求**：
```
GET /api/v1/public/search/tags
```

**参数**：
- `keyword`: 搜索关键词
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20

**响应**：
```json
{
  "code": 200,
  "message": "搜索成功",
  "data": {
    "tags": [
      {
        "id": 1,
        "name": "calculator",
        "description": "计算器相关应用",
        "app_count": 15,
        "created_at": "2023-09-01T00:00:00Z"
      }
    ],
    "total": 10,
    "page": 1,
    "page_size": 20
  }
}
```

### 标签建议

**请求**：
```
GET /api/v1/public/search/tags/suggest?prefix=calc&limit=10
```

**参数**：
- `prefix`: 搜索前缀（必填）
- `limit`: 返回数量限制，默认10

**响应**：
```json
{
  "code": 200,
  "message": "获取建议成功",
  "data": {
    "suggestions": ["calculator", "calendar"]
  }
}
```

## 管理员搜索接口

### 搜索用户（管理员）

**请求**：
```
GET /api/v1/admin/search/users
```

**请求头**：
```
Authorization: Bearer {token}
```

**参数**：
- `keyword`: 搜索关键词（用户名、邮箱、开发者信息）
- `role`: 用户角色（user/developer/admin/operator/reviewer）
- `status`: 用户状态（active/inactive/banned）
- `is_developer`: 是否为开发者（true/false）
- `verify_status`: 开发者认证状态（pending/approved/rejected）
- `start_date`: 注册开始日期（YYYY-MM-DD）
- `end_date`: 注册结束日期（YYYY-MM-DD）
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20

**响应**：
```json
{
  "code": 200,
  "message": "搜索成功",
  "data": {
    "users": [
      {
        "id": 1,
        "username": "johndoe",
        "email": "<EMAIL>",
        "phone": "***********",
        "role": "developer",
        "status": "active",
        "is_developer": true,
        "developer_name": "John Developer",
        "company_name": "John Inc",
        "verify_status": "approved",
        "created_at": "2023-09-01T00:00:00Z"
      }
    ],
    "total": 100,
    "page": 1,
    "page_size": 20
  }
}
```

### 获取标签统计（管理员）

**请求**：
```
GET /api/v1/admin/search/tags/stats
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "tag_name": "calculator",
      "app_count": 15,
      "total_downloads": 50000,
      "avg_rating": 4.2
    },
    {
      "tag_name": "game",
      "app_count": 120,
      "total_downloads": 2000000,
      "avg_rating": 4.5
    }
  ]
}
```

### 初始化搜索索引（管理员）

**请求**：
```
POST /api/v1/admin/search/init
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "搜索索引初始化成功",
  "data": null
}
```

### 同步搜索索引（管理员）

**请求**：
```
POST /api/v1/admin/search/sync
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "搜索索引同步成功",
  "data": null
}
```

### 初始化所有搜索索引（管理员）

**请求**：
```
POST /api/v1/admin/search/initialize-all
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "所有搜索索引初始化成功",
  "data": null
}
```

## 通知模块

### 获取通知列表

**请求**：
```
GET /api/v1/notifications
```

**请求头**：
```
Authorization: Bearer {token}
```

**参数**：
- `page`：页码，默认1
- `page_size`：每页数量，默认20
- `type`：通知类型过滤
- `read`：是否已读（true/false）

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "notifications": [
      {
        "id": 1,
        "user_id": 123,
        "type": "app_review",
        "title": "应用审核通知",
        "content": "您的应用《计算器Pro》已通过审核",
        "data": {
          "app_id": 1,
          "app_name": "计算器Pro"
        },
        "read": false,
        "created_at": "2023-10-01T12:00:00Z",
        "read_at": null
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 50,
      "total_pages": 3
    }
  }
}
```

### 标记通知为已读

**请求**：
```
POST /api/v1/notifications/read
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "notification_ids": [1, 2, 3]  // 通知ID数组
}
```

**响应**：
```json
{
  "code": 200,
  "message": "标记成功",
  "data": null
}
```

### 标记所有通知为已读

**请求**：
```
POST /api/v1/notifications/read-all
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "标记成功",
  "data": null
}
```

### 获取未读通知数量

**请求**：
```
GET /api/v1/notifications/unread-count
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "unread_count": 5
  }
}
```

### 获取通知设置

**请求**：
```
GET /api/v1/notifications/settings
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "email_notifications": true,
    "push_notifications": true,
    "app_review_notifications": true,
    "comment_notifications": true,
    "system_notifications": true
  }
}
```

### 更新通知设置

**请求**：
```
PUT /api/v1/notifications/settings
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "email_notifications": false,
  "push_notifications": true,
  "app_review_notifications": true,
  "comment_notifications": false,
  "system_notifications": true
}
```

**响应**：
```json
{
  "code": 200,
  "message": "更新成功",
  "data": null
}
```

### 删除通知

**请求**：
```
DELETE /api/v1/notifications/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

## 消息队列模块

### 发送通知消息

**请求**：
```
POST /api/v1/messages/notification
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "user_id": 123,
  "type": "app_review",
  "title": "应用审核通知",
  "content": "您的应用已通过审核",
  "data": {
    "app_id": 1,
    "app_name": "计算器Pro"
  }
}
```

**响应**：
```json
{
  "code": 200,
  "message": "消息发送成功",
  "data": null
}
```

### 发送邮件

**请求**：
```
POST /api/v1/messages/email
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "to": "<EMAIL>",
  "subject": "应用审核通知",
  "content": "您的应用已通过审核",
  "template": "app_review"
}
```

**响应**：
```json
{
  "code": 200,
  "message": "邮件发送成功",
  "data": null
}
```

### 记录用户活动

**请求**：
```
POST /api/v1/messages/activity
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "user_id": 123,
  "action": "app_create",
  "description": "创建了新应用",
  "metadata": {
    "app_id": 1,
    "app_name": "计算器Pro"
  }
}
```

**响应**：
```json
{
  "code": 200,
  "message": "活动记录成功",
  "data": null
}
```

### 触发应用审核

**请求**：
```
POST /api/v1/messages/app-review/{app_id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "审核流程已触发",
  "data": null
}
```

### 获取队列状态

**请求**：
```
GET /api/v1/messages/status
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "queues": [
      {
        "name": "notification_queue",
        "message_count": 10,
        "consumer_count": 2
      },
      {
        "name": "email_queue",
        "message_count": 5,
        "consumer_count": 1
      }
    ]
  }
}
```

## 地理位置模块

### 获取国家列表

**请求**：
```
GET /api/v1/public/geographic/country
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "code": "CN",
      "name": "中国",
      "name_en": "China"
    },
    {
      "code": "US",
      "name": "美国",
      "name_en": "United States"
    }
  ]
}
```

### 获取省份列表

**请求**：
```
GET /api/v1/public/geographic/province?country=CN
```

**参数**：
- `country`：国家代码（可选）

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "code": "110000",
      "name": "北京市",
      "country_code": "CN"
    },
    {
      "code": "120000",
      "name": "天津市",
      "country_code": "CN"
    }
  ]
}
```

### 获取城市列表

**请求**：
```
GET /api/v1/public/geographic/city/{province}
```

**参数**：
- `province`：省份代码（路径参数）

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "code": "110100",
      "name": "北京市",
      "province_code": "110000"
    }
  ]
}
```

### 获取区县列表

**请求**：
```
GET /api/v1/public/geographic/district/{city}
```

**参数**：
- `city`：城市代码（路径参数）

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "code": "110101",
      "name": "东城区",
      "city_code": "110100"
    },
    {
      "code": "110102",
      "name": "西城区",
      "city_code": "110100"
    }
  ]
}
```

### 获取街道列表

**请求**：
```
GET /api/v1/public/geographic/street/{district}
```

**参数**：
- `district`：区县代码（路径参数）

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "code": "110101001",
      "name": "东华门街道",
      "district_code": "110101"
    }
  ]
}
```

### 按级别获取地理信息

**请求**：
```
GET /api/v1/public/geographic/level?level=province&parent_code=CN
```

**参数**：
- `level`：级别（country/province/city/district/street）
- `parent_code`：父级代码（可选）

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "code": "110000",
      "name": "北京市",
      "parent_code": "CN",
      "level": "province"
    }
  ]
}
```

## OpenHarmony版本管理模块

### 创建OpenHarmony版本（管理员）

**请求**：
```
POST /api/v1/admin/harmony-versions
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "version_name": "OpenHarmony 4.0",
  "version_code": "4.0.0",
  "api_level": 10,
  "release_date": "2023-10-01",
  "description": "OpenHarmony 4.0 正式版",
  "is_stable": true,
  "min_sdk_version": 8,
  "target_sdk_version": 10
}
```

**响应**：
```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 1,
    "version_name": "OpenHarmony 4.0",
    "version_code": "4.0.0",
    "api_level": 10,
    "release_date": "2023-10-01T00:00:00Z",
    "description": "OpenHarmony 4.0 正式版",
    "is_stable": true,
    "min_sdk_version": 8,
    "target_sdk_version": 10,
    "created_at": "2023-10-01T12:00:00Z"
  }
}
```

### 获取OpenHarmony版本列表（管理员）

**请求**：
```
GET /api/v1/admin/harmony-versions
```

**请求头**：
```
Authorization: Bearer {token}
```

**参数**：
- `page`：页码，默认1
- `page_size`：每页数量，默认20
- `is_stable`：是否稳定版（true/false）

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "versions": [
      {
        "id": 1,
        "version_name": "OpenHarmony 4.0",
        "version_code": "4.0.0",
        "api_level": 10,
        "release_date": "2023-10-01T00:00:00Z",
        "description": "OpenHarmony 4.0 正式版",
        "is_stable": true,
        "min_sdk_version": 8,
        "target_sdk_version": 10,
        "created_at": "2023-10-01T12:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 10,
      "total_pages": 1
    }
  }
}
```

### 获取OpenHarmony版本详情（管理员）

**请求**：
```
GET /api/v1/admin/harmony-versions/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "version_name": "OpenHarmony 4.0",
    "version_code": "4.0.0",
    "api_level": 10,
    "release_date": "2023-10-01T00:00:00Z",
    "description": "OpenHarmony 4.0 正式版",
    "is_stable": true,
    "min_sdk_version": 8,
    "target_sdk_version": 10,
    "created_at": "2023-10-01T12:00:00Z",
    "updated_at": "2023-10-01T12:00:00Z"
  }
}
```

### 更新OpenHarmony版本（管理员）

**请求**：
```
PUT /api/v1/admin/harmony-versions/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "version_name": "OpenHarmony 4.0.1",
  "description": "OpenHarmony 4.0.1 修复版",
  "is_stable": true
}
```

**响应**：
```json
{
  "code": 200,
  "message": "更新成功",
  "data": null
}
```

### 删除OpenHarmony版本（管理员）

**请求**：
```
DELETE /api/v1/admin/harmony-versions/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

## 浏览历史模块

### 创建浏览记录

**请求**：
```
POST /api/v1/browse-history
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "app_id": 1
}
```

**响应**：
```json
{
  "code": 200,
  "message": "记录成功",
  "data": null
}
```

### 获取我的浏览历史

**请求**：
```
GET /api/v1/browse-history
```

**请求头**：
```
Authorization: Bearer {token}
```

**参数**：
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "app_id": 1,
        "app_name": "计算器Pro",
        "app_icon": "https://example.com/icon.jpg",
        "browsed_at": "2023-09-20T12:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 15,
      "total_pages": 1
    }
  }
}
```

### 删除浏览记录

**请求**：
```
DELETE /api/v1/browse-history/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

### 清空我的浏览历史

**请求**：
```
DELETE /api/v1/browse-history
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "清空成功",
  "data": null
}
```

### 获取浏览统计

**请求**：
```
GET /api/v1/browse-history/stats
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total_count": 50,
    "today_count": 5,
    "week_count": 20,
    "month_count": 45
  }
}
```
## 意见反馈模块

### 创建反馈

**请求**：
```
POST /api/v1/feedback
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "title": "应用闪退问题",
  "content": "在使用计算器应用时经常出现闪退现象",
  "type": "bug",
  "category_id": 1,
  "contact_email": "<EMAIL>",
  "contact_phone": "***********",
  "device_info": "华为 Mate 40 Pro",
  "system_version": "HarmonyOS 3.0",
  "app_version": "1.2.0"
}
```

**响应**：
```json
{
  "code": 200,
  "message": "反馈提交成功",
  "data": {
    "id": 1,
    "title": "应用闪退问题",
    "content": "在使用计算器应用时经常出现闪退现象",
    "type": "bug",
    "status": "pending",
    "priority": "normal",
    "created_at": "2023-09-20T12:00:00Z"
  }
}
```

### 获取我的反馈

**请求**：
```
GET /api/v1/feedback
```

**请求头**：
```
Authorization: Bearer {token}
```

**参数**：
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20
- `type`: 反馈类型（bug/feature/improvement/complaint/other）
- `status`: 反馈状态（pending/processing/resolved/closed/rejected）

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "title": "应用闪退问题",
        "type": "bug",
        "status": "pending",
        "priority": "normal",
        "created_at": "2023-09-20T12:00:00Z",
        "updated_at": "2023-09-20T12:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 5,
      "total_pages": 1
    }
  }
}
```

### 获取反馈详情

**请求**：
```
GET /api/v1/feedback/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "title": "应用闪退问题",
    "content": "在使用计算器应用时经常出现闪退现象",
    "type": "bug",
    "status": "resolved",
    "priority": "high",
    "contact_email": "<EMAIL>",
    "device_info": "华为 Mate 40 Pro",
    "system_version": "HarmonyOS 3.0",
    "resolution": "已修复闪退问题",
    "created_at": "2023-09-20T12:00:00Z",
    "resolved_at": "2023-09-21T10:00:00Z",
    "replies": [
      {
        "id": 1,
        "content": "感谢您的反馈，我们已经定位到问题并将在下个版本修复",
        "admin_name": "技术支持",
        "created_at": "2023-09-20T15:00:00Z"
      }
    ]
  }
}
```

### 获取反馈分类列表

**请求**：
```
GET /api/v1/public/feedback/categories
```

**参数**：
- `is_active`: 是否激活（可选，boolean类型）

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "name": "功能问题",
      "description": "应用功能相关的问题和建议",
      "icon": "bug",
      "color": "#ff6b6b",
      "sort_order": 1,
      "is_active": true,
      "created_at": "2023-09-20T12:00:00Z",
      "updated_at": "2023-09-20T12:00:00Z"
    },
    {
      "id": 2,
      "name": "界面优化",
      "description": "用户界面和体验相关的建议",
      "icon": "design",
      "color": "#4ecdc4",
      "sort_order": 2,
      "is_active": true,
      "created_at": "2023-09-20T12:00:00Z",
      "updated_at": "2023-09-20T12:00:00Z"
    }
  ]
}
```

### 管理员接口

#### 获取所有反馈（管理员）

**请求**：
```
GET /api/v1/admin/feedback
```

**请求头**：
```
Authorization: Bearer {token}
```

**参数**：
- `page`: 页码，默认1
- `page_size`: 每页数量，默认20
- `type`: 反馈类型
- `status`: 反馈状态
- `priority`: 优先级
- `user_id`: 用户ID
- `keyword`: 搜索关键词

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "title": "应用闪退问题",
        "type": "bug",
        "status": "pending",
        "priority": "normal",
        "user_id": 123,
        "username": "testuser",
        "created_at": "2023-09-20T12:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 50,
      "total_pages": 3
    }
  }
}
```

#### 更新反馈状态（管理员）

**请求**：
```
PUT /api/v1/admin/feedback/{id}/status
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "status": "resolved",
  "priority": "high",
  "resolution": "问题已修复"
}
```

**响应**：
```json
{
  "code": 200,
  "message": "状态更新成功",
  "data": null
}
```

#### 回复反馈（管理员）

**请求**：
```
POST /api/v1/admin/feedback/{id}/reply
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "content": "感谢您的反馈，我们会尽快处理这个问题"
}
```

**响应**：
```json
{
  "code": 200,
  "message": "回复成功",
  "data": {
    "id": 1,
    "content": "感谢您的反馈，我们会尽快处理这个问题",
    "admin_name": "技术支持",
    "created_at": "2023-09-20T15:00:00Z"
  }
}
```

#### 创建反馈分类（管理员）

**请求**：
```
POST /api/v1/admin/feedback/categories
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "name": "功能问题",
  "description": "应用功能相关的问题和建议",
  "icon": "bug",
  "sort_order": 1,
  "is_active": true
}
```

**响应**：
```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 1,
    "name": "功能问题",
    "description": "应用功能相关的问题和建议",
    "icon": "bug",
    "color": "#ff6b6b",
    "sort_order": 1,
    "is_active": true,
    "created_at": "2023-09-20T12:00:00Z",
    "updated_at": "2023-09-20T12:00:00Z"
  }
}
```

#### 更新反馈分类（管理员）

**请求**：
```
PUT /api/v1/admin/feedback/categories/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "name": "功能改进",
  "description": "应用功能改进相关的建议",
  "icon": "enhancement",
  "sort_order": 2,
  "is_active": true
}
```

**响应**：
```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": 1,
    "name": "功能改进",
    "description": "应用功能改进相关的建议",
    "icon": "enhancement",
    "color": "#4ecdc4",
    "sort_order": 2,
    "is_active": true,
    "created_at": "2023-09-20T12:00:00Z",
    "updated_at": "2023-09-20T15:00:00Z"
  }
}
```

#### 删除反馈分类（管理员）

**请求**：
```
DELETE /api/v1/admin/feedback/categories/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

**错误响应**：
```json
{
  "code": 409,
  "message": "分类正在使用中，无法删除",
  "data": null
}
```

#### 获取反馈统计（管理员）

**请求**：
```
GET /api/v1/admin/feedback/stats
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "total_count": 150,
    "pending_count": 25,
    "processing_count": 10,
    "resolved_count": 100,
    "recent_trend": [
      {"date": "2023-09-14", "count": 5},
      {"date": "2023-09-15", "count": 8},
      {"date": "2023-09-16", "count": 3},
      {"date": "2023-09-17", "count": 6},
      {"date": "2023-09-18", "count": 4},
      {"date": "2023-09-19", "count": 7},
      {"date": "2023-09-20", "count": 9}
    ]
  }
}
```

## 帮助中心模块

帮助中心模块提供了用户帮助文档的管理功能，包括分类管理、文章管理等功能。支持用户查看帮助文档和管理员管理帮助内容。

### 公开接口

#### 获取帮助分类列表

**请求**：
```
GET /api/v1/public/help/categories
```

**参数**：
- `page`: 页码，默认1
- `limit`: 每页数量，默认20
- `is_active`: 是否激活状态过滤

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "name": "使用指南",
        "description": "应用使用相关的帮助文档",
        "icon": "https://example.com/icon1.png",
        "sort_order": 1,
        "is_active": true,
        "article_count": 15,
        "created_at": "2023-09-20T12:00:00Z"
      },
      {
        "id": 2,
        "name": "常见问题",
        "description": "用户常见问题解答",
        "icon": "https://example.com/icon2.png",
        "sort_order": 2,
        "is_active": true,
        "article_count": 8,
        "created_at": "2023-09-20T12:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 5,
      "total_pages": 1
    }
  }
}
```

#### 获取帮助文章列表

**请求**：
```
GET /api/v1/public/help/articles
```

**参数**：
- `page`: 页码，默认1
- `limit`: 每页数量，默认20
- `category_id`: 分类ID过滤
- `is_published`: 是否发布状态过滤
- `keyword`: 关键词搜索

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "title": "如何下载应用",
        "summary": "详细介绍如何在应用商店中下载和安装应用",
        "content": "完整的文章内容...",
        "thumbnail": "https://example.com/thumb1.jpg",
        "is_published": true,
        "is_featured": false,
        "sort_order": 1,
        "view_count": 1250,
        "like_count": 45,
        "tags": ["下载", "安装", "教程"],
        "category": {
          "id": 1,
          "name": "使用指南",
          "description": "应用使用相关的帮助文档",
          "icon": "https://example.com/icon1.png",
          "sort_order": 1,
          "is_active": true,
          "article_count": 15,
          "created_at": "2023-09-20T12:00:00Z"
        },
        "creator": {
          "id": 1,
          "username": "admin",
          "avatar": "https://example.com/avatar.jpg"
        },
        "created_at": "2023-09-20T12:00:00Z",
        "updated_at": "2023-09-25T10:30:00Z",
        "published_at": "2023-09-21T09:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "page_size": 20,
      "total": 25,
      "total_pages": 2
    }
  }
}
```

#### 获取帮助文章详情

**请求**：
```
GET /api/v1/public/help/articles/{id}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "title": "如何下载应用",
    "summary": "详细介绍如何在应用商店中下载和安装应用",
    "content": "# 如何下载应用\n\n本文将详细介绍如何在NexusHub应用商店中下载和安装应用...\n\n## 步骤一：搜索应用\n\n1. 打开应用商店\n2. 在搜索框中输入应用名称\n3. 点击搜索按钮\n\n## 步骤二：选择应用\n\n...",
    "thumbnail": "https://example.com/thumb1.jpg",
    "is_published": true,
    "is_featured": false,
    "sort_order": 1,
    "view_count": 1251,
    "like_count": 45,
    "tags": ["下载", "安装", "教程"],
    "category": {
      "id": 1,
      "name": "使用指南",
      "description": "应用使用相关的帮助文档",
      "icon": "https://example.com/icon1.png",
      "sort_order": 1,
      "is_active": true,
      "article_count": 15,
      "created_at": "2023-09-20T12:00:00Z"
    },
    "creator": {
      "id": 1,
      "username": "admin",
      "avatar": "https://example.com/avatar.jpg"
    },
    "created_at": "2023-09-20T12:00:00Z",
    "updated_at": "2023-09-25T10:30:00Z",
    "published_at": "2023-09-21T09:00:00Z"
  }
}
```

### 管理员接口

#### 创建帮助分类（管理员）

**请求**：
```
POST /api/v1/admin/help/categories
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "name": "开发者指南",           // 必填，分类名称，最大100字符
  "description": "面向开发者的帮助文档", // 可选，分类描述，最大500字符
  "icon": "https://example.com/dev-icon.png", // 可选，分类图标URL，最大255字符
  "sort_order": 3,             // 可选，排序顺序
  "is_active": true            // 可选，是否激活，默认true
}
```

**响应**：
```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 3,
    "name": "开发者指南",
    "description": "面向开发者的帮助文档",
    "icon": "https://example.com/dev-icon.png",
    "sort_order": 3,
    "is_active": true,
    "article_count": 0,
    "created_at": "2023-09-30T12:00:00Z"
  }
}
```

#### 更新帮助分类（管理员）

**请求**：
```
PUT /api/v1/admin/help/categories/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "name": "开发者指南（更新）",      // 可选，分类名称，最大100字符
  "description": "面向开发者的帮助文档（已更新）", // 可选，分类描述，最大500字符
  "icon": "https://example.com/new-dev-icon.png", // 可选，分类图标URL，最大255字符
  "sort_order": 5,             // 可选，排序顺序
  "is_active": false           // 可选，是否激活
}
```

**响应**：
```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": 3,
    "name": "开发者指南（更新）",
    "description": "面向开发者的帮助文档（已更新）",
    "icon": "https://example.com/new-dev-icon.png",
    "sort_order": 5,
    "is_active": false,
    "article_count": 0,
    "created_at": "2023-09-30T12:00:00Z"
  }
}
```

#### 删除帮助分类（管理员）

**请求**：
```
DELETE /api/v1/admin/help/categories/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

#### 创建帮助文章（管理员）

**请求**：
```
POST /api/v1/admin/help/articles
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "title": "API接口使用指南",        // 必填，文章标题，最大200字符
  "summary": "详细介绍如何使用NexusHub的API接口", // 可选，文章摘要，最大500字符
  "content": "# API接口使用指南\n\n本文将详细介绍...", // 必填，文章内容（支持Markdown）
  "category_id": 3,              // 必填，分类ID
  "tags": "API,开发,接口,文档",      // 可选，标签，用逗号分隔，最大500字符
  "is_published": true,          // 可选，是否发布，默认false
  "sort_order": 1                // 可选，排序顺序
}
```

**响应**：
```json
{
  "code": 200,
  "message": "创建成功",
  "data": {
    "id": 26,
    "title": "API接口使用指南",
    "summary": "详细介绍如何使用NexusHub的API接口",
    "content": "# API接口使用指南\n\n本文将详细介绍...",
    "thumbnail": null,
    "is_published": true,
    "is_featured": false,
    "sort_order": 1,
    "view_count": 0,
    "like_count": 0,
    "tags": ["API", "开发", "接口", "文档"],
    "category": {
      "id": 3,
      "name": "开发者指南",
      "description": "面向开发者的帮助文档",
      "icon": "https://example.com/dev-icon.png",
      "sort_order": 3,
      "is_active": true,
      "article_count": 1,
      "created_at": "2023-09-30T12:00:00Z"
    },
    "creator": {
      "id": 1,
      "username": "admin",
      "avatar": "https://example.com/avatar.jpg"
    },
    "created_at": "2023-09-30T14:00:00Z",
    "updated_at": "2023-09-30T14:00:00Z",
    "published_at": "2023-09-30T14:00:00Z"
  }
}
```

#### 更新帮助文章（管理员）

**请求**：
```
PUT /api/v1/admin/help/articles/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求体**：
```json
{
  "title": "API接口使用指南（更新版）", // 必填，文章标题，最大200字符
  "summary": "详细介绍如何使用NexusHub的API接口（已更新）", // 可选，文章摘要，最大500字符
  "content": "# API接口使用指南（更新版）\n\n本文将详细介绍...", // 必填，文章内容（支持Markdown）
  "category_id": 3,              // 必填，分类ID
  "tags": "API,开发,接口,文档,更新",  // 可选，标签，用逗号分隔，最大500字符
  "is_published": true,          // 可选，是否发布
  "sort_order": 2                // 可选，排序顺序
}
```

**响应**：
```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": 26,
    "title": "API接口使用指南（更新版）",
    "summary": "详细介绍如何使用NexusHub的API接口（已更新）",
    "content": "# API接口使用指南（更新版）\n\n本文将详细介绍...",
    "thumbnail": null,
    "is_published": true,
    "is_featured": false,
    "sort_order": 2,
    "view_count": 15,
    "like_count": 3,
    "tags": ["API", "开发", "接口", "文档", "更新"],
    "category": {
      "id": 3,
      "name": "开发者指南",
      "description": "面向开发者的帮助文档",
      "icon": "https://example.com/dev-icon.png",
      "sort_order": 3,
      "is_active": true,
      "article_count": 1,
      "created_at": "2023-09-30T12:00:00Z"
    },
    "creator": {
      "id": 1,
      "username": "admin",
      "avatar": "https://example.com/avatar.jpg"
    },
    "created_at": "2023-09-30T14:00:00Z",
    "updated_at": "2023-09-30T16:30:00Z",
    "published_at": "2023-09-30T14:00:00Z"
  }
}
```

#### 删除帮助文章（管理员）

**请求**：
```
DELETE /api/v1/admin/help/articles/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```


## 轮播图管理模块

### 管理员接口

#### 获取轮播图列表

**请求**：
```
GET /api/v1/admin/carousels
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求参数**：
- `page` (query, integer): 页码，默认1
- `page_size` (query, integer): 每页数量，默认10
- `status` (query, string): 状态筛选，可选值: active, inactive, draft
- `type` (query, string): 类型筛选，可选值: app, collection, external, announcement
- `keyword` (query, string): 关键词搜索

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "title": "推荐应用",
        "description": "精选优质应用",
        "image_url": "https://example.com/image.jpg",
        "link_type": "app",
        "link_value": "123",
        "status": "active",
        "sort_order": 1,
        "start_time": "2024-01-01T00:00:00Z",
        "end_time": "2024-12-31T23:59:59Z",
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
      }
    ],
    "total": 10,
    "page": 1,
    "page_size": 10
  }
}
```

#### 创建轮播图

**请求**：
```
POST /api/v1/admin/carousels
```

**请求头**：
```
Authorization: Bearer {token}
Content-Type: application/json
```

**请求体**：
```json
{
  "title": "推荐应用",
  "description": "精选优质应用",
  "image_url": "https://example.com/image.jpg",
  "link_type": "app",
  "link_value": "123",
  "status": "active",
  "sort_order": 1,
  "start_time": "2024-01-01T00:00:00Z",
  "end_time": "2024-12-31T23:59:59Z"
}
```

**响应**：
```json
{
  "code": 201,
  "message": "创建成功",
  "data": {
    "id": 1,
    "title": "推荐应用",
    "description": "精选优质应用",
    "image_url": "https://example.com/image.jpg",
    "link_type": "app",
    "link_value": "123",
    "status": "active",
    "sort_order": 1,
    "start_time": "2024-01-01T00:00:00Z",
    "end_time": "2024-12-31T23:59:59Z",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

#### 获取轮播图详情

**请求**：
```
GET /api/v1/admin/carousels/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": 1,
    "title": "推荐应用",
    "description": "精选优质应用",
    "image_url": "https://example.com/image.jpg",
    "link_type": "app",
    "link_value": "123",
    "status": "active",
    "sort_order": 1,
    "start_time": "2024-01-01T00:00:00Z",
    "end_time": "2024-12-31T23:59:59Z",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

#### 更新轮播图

**请求**：
```
PUT /api/v1/admin/carousels/{id}
```

**请求头**：
```
Authorization: Bearer {token}
Content-Type: application/json
```

**请求体**：
```json
{
  "title": "推荐应用",
  "description": "精选优质应用",
  "image_url": "https://example.com/image.jpg",
  "link_type": "app",
  "link_value": "123",
  "status": "active",
  "sort_order": 1,
  "start_time": "2024-01-01T00:00:00Z",
  "end_time": "2024-12-31T23:59:59Z"
}
```

**响应**：
```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": 1,
    "title": "推荐应用",
    "description": "精选优质应用",
    "image_url": "https://example.com/image.jpg",
    "link_type": "app",
    "link_value": "123",
    "status": "active",
    "sort_order": 1,
    "start_time": "2024-01-01T00:00:00Z",
    "end_time": "2024-12-31T23:59:59Z",
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

#### 删除轮播图

**请求**：
```
DELETE /api/v1/admin/carousels/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

#### 批量更新轮播图排序

**请求**：
```
PUT /api/v1/admin/carousels/sort
```

**请求头**：
```
Authorization: Bearer {token}
Content-Type: application/json
```

**请求体**：
```json
[
  {
    "id": 1,
    "sort_order": 1
  },
  {
    "id": 2,
    "sort_order": 2
  }
]
```

**响应**：
```json
{
  "code": 200,
  "message": "更新成功",
  "data": null
}
```

### 公开接口

#### 获取轮播图列表

**请求**：
```
GET /api/v1/public/carousels
```

**请求参数**：
- `limit` (query, integer): 限制数量，默认10

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": [
    {
      "id": 1,
      "title": "推荐应用",
      "description": "精选优质应用",
      "image_url": "https://example.com/image.jpg",
      "link_type": "app",
      "link_value": "123",
      "sort_order": 1
    }
  ]
}
```

#### 记录轮播图点击

**请求**：
```
POST /api/v1/public/carousels/click
```

**请求头**：
```
Content-Type: application/json
```

**请求体**：
```json
{
  "carousel_id": 1,
  "user_id": 123
}
```

**响应**：
```json
{
  "code": 200,
  "message": "记录成功",
  "data": null
}
```

## 上传服务模块

### 获取上传令牌

**请求**：
```
GET /api/v1/upload/token
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求参数**：
- `file_type` (query, string): 文件类型，可选值: image, video, document, app
- `file_size` (query, integer): 文件大小（字节）
- `file_name` (query, string): 文件名

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "upload_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "upload_url": "https://upload.example.com/upload",
    "expires_at": "2024-01-01T01:00:00Z",
    "max_file_size": 10485760,
    "allowed_types": ["jpg", "png", "gif"]
  }
}
```

## 通知管理模块

### 获取通知列表

**请求**：
```
GET /api/v1/notifications
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求参数**：
- `page` (query, integer): 页码，默认1
- `limit` (query, integer): 每页数量，默认20
- `type` (query, string): 通知类型筛选
- `is_read` (query, boolean): 是否已读筛选

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": 1,
        "title": "应用审核通过",
        "content": "您的应用已通过审核",
        "type": "app_approved",
        "is_read": false,
        "created_at": "2024-01-01T00:00:00Z",
        "read_at": null
      }
    ],
    "total": 10,
    "page": 1,
    "limit": 20
  }
}
```

### 标记通知为已读

**请求**：
```
PUT /api/v1/notifications/read
```

**请求头**：
```
Authorization: Bearer {token}
Content-Type: application/json
```

**请求体**：
```json
{
  "notification_ids": [1, 2, 3]
}
```

**响应**：
```json
{
  "code": 200,
  "message": "标记成功",
  "data": null
}
```

### 标记所有通知为已读

**请求**：
```
PUT /api/v1/notifications/read-all
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "标记成功",
  "data": null
}
```

### 获取未读通知数量

**请求**：
```
GET /api/v1/notifications/unread-count
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "unread_count": 5
  }
}
```

### 获取通知设置

**请求**：
```
GET /api/v1/notifications/settings
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "email_enabled": true,
    "push_enabled": true,
    "app_updates": true,
    "review_notifications": true,
    "system_notifications": true
  }
}
```

### 更新通知设置

**请求**：
```
PUT /api/v1/notifications/settings
```

**请求头**：
```
Authorization: Bearer {token}
Content-Type: application/json
```

**请求体**：
```json
{
  "email_enabled": true,
  "push_enabled": true,
  "app_updates": true,
  "review_notifications": true,
  "system_notifications": true
}
```

**响应**：
```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "email_enabled": true,
    "push_enabled": true,
    "app_updates": true,
    "review_notifications": true,
    "system_notifications": true
  }
}
```

### 删除通知

**请求**：
```
DELETE /api/v1/notifications/{id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "删除成功",
  "data": null
}
```

## 消息队列模块

### 发送通知消息

**请求**：
```
POST /api/v1/messages/notification
```

**请求头**：
```
Authorization: Bearer {token}
Content-Type: application/json
```

**请求体**：
```json
{
  "user_id": 123,
  "title": "通知标题",
  "content": "通知内容",
  "type": "system",
  "data": {
    "app_id": 456
  }
}
```

**响应**：
```json
{
  "code": 200,
  "message": "发送成功",
  "data": null
}
```

### 发送邮件

**请求**：
```
POST /api/v1/messages/email
```

**请求头**：
```
Authorization: Bearer {token}
Content-Type: application/json
```

**请求体**：
```json
{
  "to": "<EMAIL>",
  "subject": "邮件主题",
  "template": "welcome",
  "data": {
    "username": "张三",
    "app_name": "测试应用"
  }
}
```

**响应**：
```json
{
  "code": 200,
  "message": "发送成功",
  "data": null
}
```

### 记录用户活动

**请求**：
```
POST /api/v1/messages/activity
```

**请求头**：
```
Authorization: Bearer {token}
Content-Type: application/json
```

**请求体**：
```json
{
  "user_id": 123,
  "action": "app_download",
  "resource_type": "app",
  "resource_id": 456,
  "metadata": {
    "version": "1.0.0",
    "platform": "android"
  }
}
```

**响应**：
```json
{
  "code": 200,
  "message": "记录成功",
  "data": null
}
```

### 触发应用审核

**请求**：
```
POST /api/v1/messages/app-review/{app_id}
```

**请求头**：
```
Authorization: Bearer {token}
```

**请求参数**：
- `action` (query, string): 操作类型，可选值: submit, approve, reject
- `reviewer_id` (query, integer): 审核员ID
- `comment` (query, string): 审核意见

**响应**：
```json
{
  "code": 200,
  "message": "触发成功",
  "data": null
}
```

### 获取队列状态

**请求**：
```
GET /api/v1/messages/status
```

**请求头**：
```
Authorization: Bearer {token}
```

**响应**：
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "notification_queue": {
      "pending": 10,
      "processing": 2,
      "failed": 1
    },
    "email_queue": {
      "pending": 5,
      "processing": 1,
      "failed": 0
    },
    "activity_queue": {
      "pending": 20,
      "processing": 3,
      "failed": 2
    }
  }
}
```

### 设置应用推荐状态（管理员）

**请求**：
```
PUT /api/v1/admin/apps/{id}/recommended
```

**请求头**：
```
Authorization: Bearer {token}
Content-Type: application/json
```

**路径参数**：
- `id` (integer): 应用ID

**请求体**：
```json
{
  "is_recommended": true
}
```

**字段说明**：
- `is_recommended` (boolean): 是否推荐，必填

**响应**：
```json
{
  "code": 200,
  "message": "设置成功",
  "data": null
}
```

**错误响应**：
- `400`: 参数错误
- `404`: 应用不存在
- `500`: 服务器错误

## 系统接口

### 健康检查

**请求**：
```
GET /health
```

**响应**：
```json
{
  "status": "ok",
  "time": "2023-09-30T12:00:00Z"
}
```

### 获取配置信息

**请求**：
```
GET /config
```

**响应**：
```json
{
  "server_port": "8080",
  "server_mode": "release",
  "db_host": "localhost",
  "redis_addr": "localhost:6379",
  "storage_type": "local",
  "storage_path": "/data"
}
```
